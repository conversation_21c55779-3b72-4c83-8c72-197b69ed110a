/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/ManageTrackSheet.tsx":
/*!***************************************************!*\
  !*** ./app/user/trackSheets/ManageTrackSheet.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _createTracksheet_createTrackSheet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createTracksheet/createTrackSheet */ \"(app-pages-browser)/./app/user/trackSheets/createTracksheet/createTrackSheet.tsx\");\n/* harmony import */ var _createTracksheet_createTrackSheet__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_createTracksheet_createTrackSheet__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/TrackSheetContext.tsx\");\n/* harmony import */ var _components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sidebar/Sidebar */ \"(app-pages-browser)/./components/sidebar/Sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/_component/BreadCrumbs */ \"(app-pages-browser)/./app/_component/BreadCrumbs.tsx\");\n/* harmony import */ var _TrackSheetTabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TrackSheetTabs */ \"(app-pages-browser)/./app/user/trackSheets/TrackSheetTabs.tsx\");\n/* harmony import */ var _ImportModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ImportModal */ \"(app-pages-browser)/./app/user/trackSheets/ImportModal.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/ClientSelectPage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst ManageWorkSheet = (param)=>{\n    let { permissions, client, associate, userData, actions, carrierDataUpdate, clientDataUpdate, carrier, legrandsData } = param;\n    _s();\n    const [filterdata, setFilterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [deleteData, setDeletedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"view\");\n    const [customFieldsReloadTrigger, setCustomFieldsReloadTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isImportModalOpen, setImportModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAssociateId, setSelectedAssociateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedClientId, setSelectedClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [warningFilter, setWarningFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const associateOptions = (associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n        var _a_id;\n        return {\n            value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n            label: a.name,\n            name: a.name\n        };\n    })) || [];\n    const clientOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!selectedAssociateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === selectedAssociateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    }, [\n        client,\n        selectedAssociateId\n    ]);\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            filterdata,\n            setFilterData,\n            deleteData,\n            setDeletedData,\n            setCustomFieldsReloadTrigger,\n            customFieldsReloadTrigger,\n            warningFilter,\n            setWarningFilter\n        }), [\n        filterdata,\n        setFilterData,\n        deleteData,\n        setDeletedData,\n        setCustomFieldsReloadTrigger,\n        customFieldsReloadTrigger,\n        warningFilter,\n        setWarningFilter\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_3__.TrackSheetContext.Provider, {\n        value: contextValue,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        permissions: permissions,\n                        profile: userData\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 w-full pl-3 overflow-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    breadcrumblist: [\n                                        {\n                                            link: \"/user/trackSheets\",\n                                            name: \"TrackSheet\"\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TrackSheetTabs__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                activeView: activeView,\n                                setActiveView: setActiveView,\n                                isImportModalOpen: isImportModalOpen,\n                                setImportModalOpen: setImportModalOpen,\n                                selectedAssociateId: selectedAssociateId,\n                                setSelectedAssociateId: setSelectedAssociateId,\n                                selectedClientId: selectedClientId,\n                                setSelectedClientId: setSelectedClientId,\n                                associateOptions: associateOptions,\n                                clientOptions: clientOptions\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImportModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                isOpen: isImportModalOpen,\n                                onClose: ()=>setImportModalOpen(false),\n                                userData: userData\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, undefined),\n                            activeView === \"create\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((_createTracksheet_createTrackSheet__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                client: client,\n                                associate: associate,\n                                userData: userData,\n                                activeView: activeView,\n                                setActiveView: setActiveView,\n                                permissions: actions,\n                                carrierDataUpdate: carrierDataUpdate,\n                                clientDataUpdate: clientDataUpdate,\n                                carrier: carrier,\n                                associateId: selectedAssociateId,\n                                clientId: selectedClientId,\n                                legrandsData: legrandsData\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    permissions: actions,\n                                    client: client,\n                                    clientDataUpdate: clientDataUpdate,\n                                    carrierDataUpdate: carrierDataUpdate,\n                                    userData: userData\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ManageWorkSheet, \"xxclkiAMH4HSeJPlnp5KbfHvEZY=\");\n_c = ManageWorkSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ManageWorkSheet);\nvar _c;\n$RefreshReg$(_c, \"ManageWorkSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/ManageTrackSheet.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/user/trackSheets/createTracksheet/createTrackSheet.tsx":
/*!********************************************************************!*\
  !*** ./app/user/trackSheets/createTracksheet/createTrackSheet.tsx ***!
  \********************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {



;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ })

});