"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/createTracksheet/createTrackSheet.tsx":
/*!********************************************************************!*\
  !*** ./app/user/trackSheets/createTracksheet/createTrackSheet.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/ClientSelectPage.tsx\");\n/* harmony import */ var _components_TracksheetEntryForm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./components/TracksheetEntryForm */ \"(app-pages-browser)/./app/user/trackSheets/createTracksheet/components/TracksheetEntryForm.tsx\");\n/* harmony import */ var _hooks_useTracksheetLogic__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./hooks/useTracksheetLogic */ \"(app-pages-browser)/./app/user/trackSheets/createTracksheet/hooks/useTracksheetLogic.ts\");\n/* harmony import */ var _hooks_useFilenameGenerator__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./hooks/useFilenameGenerator */ \"(app-pages-browser)/./app/user/trackSheets/createTracksheet/hooks/useFilenameGenerator.ts\");\n/* harmony import */ var _utils_createTracksheetSubmit__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./utils/createTracksheetSubmit */ \"(app-pages-browser)/./app/user/trackSheets/createTracksheet/utils/createTracksheetSubmit.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst FIELD_OPTIONS = [\n    \"ASSOCIATE\",\n    \"CLIENT\",\n    \"ADDITIONALFOLDERNAME\",\n    \"CARRIER\",\n    \"YEAR\",\n    \"MONTH\",\n    \"RECEIVE DATE\",\n    \"FTP FILE NAME\"\n];\nconst isField = (value)=>FIELD_OPTIONS.includes(value);\nconst validateFtpPageFormat = (value)=>{\n    if (!value || value.trim() === \"\") return false;\n    const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n    const match = value.match(ftpPageRegex);\n    if (!match) return false;\n    const currentPage = parseInt(match[1], 10);\n    const totalPages = parseInt(match[2], 10);\n    return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;\n};\nconst validateDateFormat = (value)=>{\n    if (!value || value.trim() === \"\") return true;\n    const dateRegex = /^(\\d{1,2})\\/(\\d{1,2})\\/(\\d{4})$/;\n    const match = value.match(dateRegex);\n    if (!match) return false;\n    const day = parseInt(match[1], 10);\n    const month = parseInt(match[2], 10);\n    const year = parseInt(match[3], 10);\n    if (month < 1 || month > 12) return false;\n    if (day < 1 || day > 31) return false;\n    if (year < 1900 || year > 2100) return false;\n    const date = new Date(year, month - 1, day);\n    return date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day;\n};\nconst trackSheetSchema = zod__WEBPACK_IMPORTED_MODULE_15__.z.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Client is required\"),\n    entries: zod__WEBPACK_IMPORTED_MODULE_15__.z.array(zod__WEBPACK_IMPORTED_MODULE_15__.z.object({\n        company: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Company is required\"),\n        division: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        invoice: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice is required\"),\n        masterInvoice: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        bol: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        invoiceDate: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice date is required\").refine(validateDateFormat, \"Please enter a valid date in DD/MM/YYYY format\"),\n        receivedDate: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Received date is required\").refine(validateDateFormat, \"Please enter a valid date in DD/MM/YYYY format\"),\n        shipmentDate: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional().refine((value)=>!value || validateDateFormat(value), \"Please enter a valid date in DD/MM/YYYY format\"),\n        carrierName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Carrier name is required\"),\n        invoiceStatus: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice status is required\"),\n        manualMatching: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Manual matching is required\"),\n        invoiceType: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice type is required\"),\n        billToClient: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        finalInvoice: zod__WEBPACK_IMPORTED_MODULE_15__.z.boolean().optional(),\n        currency: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Currency is required\"),\n        qtyShipped: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        weightUnitName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        quantityBilledText: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        freightClass: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        invoiceTotal: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"Invoice total is required\"),\n        savings: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        financialNotes: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        fileId: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        ftpFileName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"FTP File Name is required\"),\n        ftpPage: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().min(1, \"FTP Page is required\").refine((value)=>validateFtpPageFormat(value), (value)=>{\n            if (!value || value.trim() === \"\") {\n                return {\n                    message: \"FTP Page is required\"\n                };\n            }\n            const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n            const match = value.match(ftpPageRegex);\n            if (!match) {\n                return {\n                    message: \"\"\n                };\n            }\n            const currentPage = parseInt(match[1], 10);\n            const totalPages = parseInt(match[2], 10);\n            if (currentPage <= 0 || totalPages <= 0) {\n                return {\n                    message: \"Page numbers must be positive (greater than 0)\"\n                };\n            }\n            if (currentPage > totalPages) {\n                return {\n                    message: \"Please enter a page number between \".concat(totalPages, \" and \").concat(currentPage, \" \")\n                };\n            }\n            return {\n                message: \"Invalid page format\"\n            };\n        }),\n        docAvailable: zod__WEBPACK_IMPORTED_MODULE_15__.z.array(zod__WEBPACK_IMPORTED_MODULE_15__.z.string()).optional().default([]),\n        otherDocuments: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        notes: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandCompanyName: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoAlias: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoAddress: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoZipcode: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        shipperType: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        consigneeType: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        billtoType: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        legrandFreightTerms: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n        customFields: zod__WEBPACK_IMPORTED_MODULE_15__.z.array(zod__WEBPACK_IMPORTED_MODULE_15__.z.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_15__.z.string(),\n            name: zod__WEBPACK_IMPORTED_MODULE_15__.z.string(),\n            type: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional(),\n            value: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional()\n        })).default([]),\n        enteredBy: zod__WEBPACK_IMPORTED_MODULE_15__.z.string().optional()\n    }).refine((entry)=>{\n        if (validateDateFormat(entry.invoiceDate) && validateDateFormat(entry.receivedDate)) {\n            const [invDay, invMonth, invYear] = entry.invoiceDate.split(\"/\").map(Number);\n            const [recDay, recMonth, recYear] = entry.receivedDate.split(\"/\").map(Number);\n            const invoiceDateObj = new Date(invYear, invMonth - 1, invDay);\n            const receivedDateObj = new Date(recYear, recMonth - 1, recDay);\n            return invoiceDateObj <= receivedDateObj;\n        }\n        return true;\n    }, {\n        message: \"The invoice date should be older than or the same as the received date.\",\n        path: [\n            \"invoiceDate\"\n        ]\n    }).refine((entry)=>{\n        var _entry_company;\n        if ((_entry_company = entry.company) === null || _entry_company === void 0 ? void 0 : _entry_company.toUpperCase().includes(\"LEGRAND\")) {\n            return entry.legrandFreightTerms && entry.legrandFreightTerms.trim() !== \"\";\n        }\n        return true;\n    }, {\n        message: \"Freight Term is required for LEGRAND clients.\",\n        path: [\n            \"legrandFreightTerms\"\n        ]\n    }).refine((entry)=>{\n        var _entry_company;\n        if (entry.company === \"LEGRAND\" || ((_entry_company = entry.company) === null || _entry_company === void 0 ? void 0 : _entry_company.includes(\"LEGRAND\"))) {\n            return entry.shipperType && entry.consigneeType && entry.billtoType;\n        }\n        return true;\n    }, {\n        message: \"DC/CV selection is required for all Legrand blocks.\",\n        path: [\n            \"shipperType\"\n        ]\n    }).refine((entry)=>{\n        var _entry_company;\n        if (entry.company === \"LEGRAND\" || ((_entry_company = entry.company) === null || _entry_company === void 0 ? void 0 : _entry_company.includes(\"LEGRAND\"))) {\n            return entry.consigneeType && entry.consigneeType.trim() !== \"\";\n        }\n        return true;\n    }, {\n        message: \"DC/CV is required for LEGRAND clients.\",\n        path: [\n            \"consigneeType\"\n        ]\n    }).refine((entry)=>{\n        var _entry_company;\n        if (entry.company === \"LEGRAND\" || ((_entry_company = entry.company) === null || _entry_company === void 0 ? void 0 : _entry_company.includes(\"LEGRAND\"))) {\n            return entry.billtoType && entry.billtoType.trim() !== \"\";\n        }\n        return true;\n    }, {\n        message: \"DC/CV is required for LEGRAND clients.\",\n        path: [\n            \"billtoType\"\n        ]\n    }))\n}).refine((data)=>{\n    for(let i = 0; i < data.entries.length; i++){\n        var _entry_company;\n        const entry = data.entries[i];\n        if (entry.company === \"LEGRAND\" || ((_entry_company = entry.company) === null || _entry_company === void 0 ? void 0 : _entry_company.includes(\"LEGRAND\"))) {\n            if (!entry.freightClass || entry.freightClass.trim() === \"\" || !entry.shipperType || entry.shipperType.trim() === \"\" || !entry.consigneeType || entry.consigneeType.trim() === \"\" || !entry.billtoType || entry.billtoType.trim() === \"\" || !entry.legrandFreightTerms || entry.legrandFreightTerms.trim() === \"\") {\n                return false;\n            }\n        }\n    }\n    return true;\n}, {\n    message: \"Billing type and DC/CV selections are required for all Legrand blocks.\",\n    path: [\n        \"entries\"\n    ]\n});\nconst CreateTrackSheet = (param)=>{\n    let { client, associate, userData, activeView, setActiveView, permissions, carrierDataUpdate, clientDataUpdate, carrier, associateId, clientId, legrandsData } = param;\n    _s();\n    const userName = userData === null || userData === void 0 ? void 0 : userData.username;\n    const companyFieldRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [isImportModalOpen, setImportModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clientFilePathFormat, setClientFilePathFormat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [existingEntries, setExistingEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [generatedFilenames, setGeneratedFilenames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filenameValidation, setFilenameValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [missingFields, setMissingFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [manualMatchingData, setManualMatchingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customFieldsRefresh, setCustomFieldsRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showFullForm, setShowFullForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [assignedFiles, setAssignedFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [carrierByClient, setCarrierByClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleChange = async (id)=>{\n        try {\n            const carrierByClientData = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_5__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_6__.carrier_routes.GET_CARRIER_BY_CLIENT, \"/\").concat(id));\n            if (carrierByClientData && Array.isArray(carrierByClientData)) {\n                const formattedCarriers = carrierByClientData.map((item)=>{\n                    var _item_carrier_id, _item_carrier, _item_carrier1;\n                    return {\n                        value: (_item_carrier = item.carrier) === null || _item_carrier === void 0 ? void 0 : (_item_carrier_id = _item_carrier.id) === null || _item_carrier_id === void 0 ? void 0 : _item_carrier_id.toString(),\n                        label: (_item_carrier1 = item.carrier) === null || _item_carrier1 === void 0 ? void 0 : _item_carrier1.name\n                    };\n                }).filter((carrier)=>carrier.value && carrier.label);\n                formattedCarriers.sort((a, b)=>a.label.localeCompare(b.label));\n                setCarrierByClient(formattedCarriers);\n            } else {\n                setCarrierByClient([]);\n            }\n        } catch (error) {\n            /* eslint-disable */ console.error(...oo_tx(\"2922015453_380_6_380_58_11\", \"Error fetching carrier data:\", error));\n            setCarrierByClient([]);\n        }\n    };\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(trackSheetSchema),\n        defaultValues: {\n            associateId: associateId || \"\",\n            clientId: clientId || \"\",\n            entries: [\n                {\n                    company: \"\",\n                    division: \"\",\n                    invoice: \"\",\n                    masterInvoice: \"\",\n                    bol: \"\",\n                    invoiceDate: \"\",\n                    receivedDate: \"\",\n                    shipmentDate: \"\",\n                    carrierName: \"\",\n                    invoiceStatus: \"ENTRY\",\n                    manualMatching: \"\",\n                    invoiceType: \"\",\n                    billToClient: \"yes\",\n                    finalInvoice: false,\n                    currency: \"\",\n                    qtyShipped: \"\",\n                    weightUnitName: \"\",\n                    quantityBilledText: \"\",\n                    freightClass: \"\",\n                    invoiceTotal: \"\",\n                    savings: \"\",\n                    financialNotes: \"\",\n                    fileId: \"\",\n                    ftpFileName: \"\",\n                    ftpPage: \"\",\n                    docAvailable: [],\n                    otherDocuments: \"\",\n                    notes: \"\",\n                    legrandAlias: \"\",\n                    legrandCompanyName: \"\",\n                    legrandAddress: \"\",\n                    legrandZipcode: \"\",\n                    shipperAlias: \"\",\n                    shipperAddress: \"\",\n                    shipperZipcode: \"\",\n                    consigneeAlias: \"\",\n                    consigneeAddress: \"\",\n                    consigneeZipcode: \"\",\n                    billtoAlias: \"\",\n                    billtoAddress: \"\",\n                    billtoZipcode: \"\",\n                    shipperType: \"\",\n                    consigneeType: \"\",\n                    billtoType: \"\",\n                    legrandFreightTerms: \"\",\n                    customFields: [],\n                    enteredBy: (userData === null || userData === void 0 ? void 0 : userData.username) || \"\"\n                }\n            ]\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const subscription = form.watch((value, param)=>{\n            let { name, type } = param;\n            if ((name === null || name === void 0 ? void 0 : name.includes(\"receivedDate\")) || (name === null || name === void 0 ? void 0 : name.includes(\"ftpFileName\"))) {}\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        form\n    ]);\n    const handleDateChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((index, value)=>{\n        form.setValue(\"entries.\".concat(index, \".receivedDate\"), value, {\n            shouldValidate: true,\n            shouldDirty: true,\n            shouldTouch: true\n        });\n    }, [\n        form\n    ]);\n    const handleFtpFileNameChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((index, value, fileId)=>{\n        form.setValue(\"entries.\".concat(index, \".ftpFileName\"), value, {\n            shouldValidate: true,\n            shouldDirty: true,\n            shouldTouch: true\n        });\n        // Store the file ID in a hidden field if needed\n        if (fileId) {\n            form.setValue(\"entries.\".concat(index, \".fileId\"), fileId, {\n                shouldValidate: true,\n                shouldDirty: true,\n                shouldTouch: true\n            });\n        }\n    }, [\n        form\n    ]);\n    const clientOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (!associateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === associateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    }, [\n        client,\n        associateId\n    ]);\n    const entries = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch)({\n        control: form.control,\n        name: \"entries\"\n    });\n    const watchedClientId = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch)({\n        control: form.control,\n        name: \"clientId\"\n    });\n    const validateClientForAssociate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, currentClientId)=>{\n        if (associateId && currentClientId) {\n            var _currentClient_associateId;\n            const currentClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === currentClientId;\n            });\n            if (currentClient && ((_currentClient_associateId = currentClient.associateId) === null || _currentClient_associateId === void 0 ? void 0 : _currentClient_associateId.toString()) !== associateId) {\n                form.setValue(\"clientId\", \"\");\n                return false;\n            }\n        }\n        return true;\n    }, [\n        client,\n        form\n    ]);\n    const clearEntrySpecificClients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const currentEntries = form.getValues(\"entries\") || [];\n        if (currentEntries.length > 0) {\n            const hasEntrySpecificClients = currentEntries.some((entry)=>entry.clientId);\n            if (hasEntrySpecificClients) {\n                const updatedEntries = currentEntries.map((entry)=>({\n                        ...entry,\n                        clientId: \"\"\n                    }));\n                form.setValue(\"entries\", updatedEntries);\n            }\n        }\n    }, [\n        form\n    ]);\n    const fetchClientFilePathFormat = async (clientId)=>{\n        try {\n            const response = await fetch(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_6__.customFilepath_routes.GET_CLIENT_CUSTOM_FILEPATH, \"?clientId=\").concat(clientId));\n            if (response.ok) {\n                const result = await response.json();\n                if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {\n                    const filepathData = result.data[0];\n                    if (filepathData && filepathData.filePath) {\n                        setClientFilePathFormat(filepathData.filePath);\n                    // setTimeout(() => updateFilenames(), 0); // REMOVE THIS\n                    } else {\n                        setClientFilePathFormat(null);\n                    }\n                } else {\n                    setClientFilePathFormat(null);\n                }\n            } else {\n                setClientFilePathFormat(null);\n            }\n        } catch (error) {\n            /* eslint-disable */ console.error(...oo_tx(\"2922015453_571_6_574_7_11\", \"[fetchClientFilePathFormat] Error fetching filePath for client:\", error));\n            setClientFilePathFormat(null);\n        }\n    };\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_5__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_6__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            if (response && Array.isArray(response.data)) {\n                setLegrandData(response.data);\n            } else {\n                setLegrandData([]);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Error fetching LEGRAND mapping data:\", error);\n            setLegrandData([]);\n        }\n    }, []);\n    const fetchManualMatchingData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_5__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_6__.manualMatchingMapping_routes.GET_MANUAL_MATCHING_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setManualMatchingData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Error fetching manual matching mapping data:\", error);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!watchedClientId) {\n            setLegrandData([]);\n            setManualMatchingData([]);\n            return;\n        }\n        const selectedClient = clientOptions.find((c)=>c.value === watchedClientId);\n        if (selectedClient && selectedClient.name.toLowerCase().includes(\"legrand\")) {\n            fetchLegrandData();\n            fetchManualMatchingData();\n        } else {\n            setLegrandData([]);\n            setManualMatchingData([]);\n        }\n    }, [\n        watchedClientId,\n        clientOptions,\n        fetchLegrandData,\n        fetchManualMatchingData\n    ]);\n    const handleLegrandDataChange = (entryIndex, businessUnit, divisionCode)=>{\n        form.setValue(\"entries.\".concat(entryIndex, \".company\"), businessUnit);\n        if (divisionCode) {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), divisionCode);\n            handleManualMatchingAutoFill(entryIndex, divisionCode);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), \"\");\n        }\n    };\n    const handleManualMatchingAutoFill = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, division)=>{\n        var _formValues_entries, _clientOptions_find;\n        if (!division || !manualMatchingData.length) {\n            return;\n        }\n        const formValues = form.getValues();\n        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        if (entryClientName !== \"LEGRAND\") {\n            return;\n        }\n        const matchingEntry = manualMatchingData.find((mapping)=>mapping.division === division);\n        if (matchingEntry && matchingEntry.ManualShipment) {\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), matchingEntry.ManualShipment);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), \"\");\n        }\n    }, [\n        form,\n        manualMatchingData,\n        clientOptions\n    ]);\n    const fetchCustomFieldsForClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (clientId)=>{\n        if (!clientId) return [];\n        try {\n            const allCustomFieldsResponse = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_5__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_6__.clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS, \"/\").concat(clientId));\n            let customFieldsData = [];\n            if (allCustomFieldsResponse && allCustomFieldsResponse.custom_fields && allCustomFieldsResponse.custom_fields.length > 0) {\n                customFieldsData = allCustomFieldsResponse.custom_fields.map((field)=>{\n                    let autoFilledValue = \"\";\n                    if (field.type === \"AUTO\") {\n                        if (field.autoOption === \"DATE\") {\n                            const today = new Date();\n                            const day = today.getDate().toString().padStart(2, \"0\");\n                            const month = (today.getMonth() + 1).toString().padStart(2, \"0\");\n                            const year = today.getFullYear();\n                            autoFilledValue = \"\".concat(day, \"/\").concat(month, \"/\").concat(year);\n                        } else if (field.autoOption === \"USERNAME\") {\n                            autoFilledValue = (userData === null || userData === void 0 ? void 0 : userData.username) || \"\";\n                        }\n                    }\n                    return {\n                        id: field.id,\n                        name: field.name,\n                        type: field.type,\n                        autoOption: field.autoOption,\n                        value: autoFilledValue\n                    };\n                });\n            }\n            return customFieldsData;\n        } catch (error) {\n            return [];\n        }\n    }, [\n        userData\n    ]);\n    const fields = [\n        {\n            id: \"single-entry\"\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (watchedClientId) {\n            fetchClientFilePathFormat(watchedClientId);\n            handleChange(watchedClientId);\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        watchedClientId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        companyFieldRefs.current = companyFieldRefs.current.slice(0, fields.length);\n    }, [\n        fields.length\n    ]);\n    const { generateFilename } = (0,_hooks_useFilenameGenerator__WEBPACK_IMPORTED_MODULE_13__.useFilenameGenerator)({\n        associate,\n        client,\n        carrier,\n        userName,\n        associateId: associateId || \"\"\n    });\n    const updateFilenames = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const formValues = form.getValues();\n        const filenames = [];\n        const validations = [];\n        const missingArr = [];\n        if (!clientFilePathFormat) {\n            return;\n        }\n        if (formValues.entries && Array.isArray(formValues.entries)) {\n            formValues.entries.forEach((entry, index)=>{\n                const { filename, isValid, missing, debug } = generateFilename(index, formValues, clientFilePathFormat // <-- pass as argument\n                );\n                filenames[index] = filename;\n                validations[index] = isValid;\n                missingArr[index] = missing || [];\n            });\n        }\n        setGeneratedFilenames(filenames);\n        setFilenameValidation(validations);\n        setMissingFields(missingArr);\n    }, [\n        form,\n        generateFilename,\n        clientFilePathFormat\n    ]);\n    // Add this effect to call updateFilenames when clientFilePathFormat changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (clientFilePathFormat) {\n            updateFilenames();\n        }\n    }, [\n        clientFilePathFormat,\n        updateFilenames\n    ]);\n    // Use hooks for dynamic logic\n    (0,_hooks_useTracksheetLogic__WEBPACK_IMPORTED_MODULE_12__.useTracksheetLogic)({\n        form,\n        clientOptions,\n        legrandData,\n        setCarrierByClient,\n        carrier,\n        associate,\n        client,\n        setClientFilePathFormat,\n        clientFilePathMap: {},\n        updateFilenames,\n        handleManualMatchingAutoFill,\n        handleLegrandDataChange\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (clientFilePathFormat && entries && Array.isArray(entries)) {\n            updateFilenames();\n        }\n    }, [\n        clientFilePathFormat,\n        entries,\n        updateFilenames\n    ]);\n    const handleCompanyAutoPopulation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, entryClientId)=>{\n        var _clientOptions_find;\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        const currentEntry = form.getValues(\"entries.\".concat(entryIndex));\n        if (entryClientName && entryClientName !== \"LEGRAND\") {\n            form.setValue(\"entries.\".concat(entryIndex, \".company\"), entryClientName);\n        } else if (entryClientName === \"LEGRAND\") {\n            // Always set company to LEGRAND for Legrand clients\n            form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"LEGRAND\");\n        } else {\n            if (currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        }\n    }, [\n        form,\n        clientOptions\n    ]);\n    const handleCustomFieldsFetch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (entryIndex, entryClientId)=>{\n        var _currentCustomFields_, _currentCustomFields_1;\n        if (!entryClientId) {\n            const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\"));\n            if (currentCustomFields && currentCustomFields.length > 0) {\n                form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), []);\n            }\n            return;\n        }\n        const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\")) || [];\n        const hasEmptyAutoUsernameFields = currentCustomFields.some((field)=>field.type === \"AUTO\" && field.autoOption === \"USERNAME\" && !field.value && (userData === null || userData === void 0 ? void 0 : userData.username));\n        const shouldFetchCustomFields = currentCustomFields.length === 0 || currentCustomFields.length > 0 && !((_currentCustomFields_ = currentCustomFields[0]) === null || _currentCustomFields_ === void 0 ? void 0 : _currentCustomFields_.clientId) || ((_currentCustomFields_1 = currentCustomFields[0]) === null || _currentCustomFields_1 === void 0 ? void 0 : _currentCustomFields_1.clientId) !== entryClientId || hasEmptyAutoUsernameFields;\n        if (shouldFetchCustomFields) {\n            const customFieldsData = await fetchCustomFieldsForClient(entryClientId);\n            const fieldsWithClientId = customFieldsData.map((field)=>({\n                    ...field,\n                    clientId: entryClientId\n                }));\n            form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), fieldsWithClientId);\n            setTimeout(()=>{\n                fieldsWithClientId.forEach((field, fieldIndex)=>{\n                    const fieldPath = \"entries.\".concat(entryIndex, \".customFields.\").concat(fieldIndex, \".value\");\n                    if (field.value) {\n                        form.setValue(fieldPath, field.value);\n                    }\n                });\n                setCustomFieldsRefresh((prev)=>prev + 1);\n            }, 100);\n        }\n    }, [\n        form,\n        fetchCustomFieldsForClient,\n        userData === null || userData === void 0 ? void 0 : userData.username\n    ]);\n    const handleInitialSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((_associateId, _clientId)=>{\n        form.setValue(\"associateId\", associateId);\n        form.setValue(\"clientId\", clientId);\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(0, clientId);\n            handleCustomFieldsFetch(0, clientId);\n        }, 50);\n        setShowFullForm(true);\n    }, [\n        form,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch,\n        associateId,\n        clientId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (associateId && clientId) {\n            setShowFullForm(true);\n            handleInitialSelection(associateId, clientId);\n        } else {\n            setShowFullForm(false);\n        }\n    }, [\n        associateId,\n        clientId,\n        handleInitialSelection\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timeoutId = setTimeout(()=>{\n            updateFilenames();\n            const formValues = form.getValues();\n            if (formValues.entries && Array.isArray(formValues.entries)) {\n                formValues.entries.forEach((entry, index)=>{\n                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (index === 0 ? formValues.clientId : \"\");\n                    if (entryClientId) {}\n                });\n            }\n        }, 50);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        updateFilenames,\n        handleCustomFieldsFetch,\n        form\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const subscription = form.watch((_, param)=>{\n            let { name } = param;\n            if (name && (name.includes(\"associateId\") || name.includes(\"clientId\") || name.includes(\"carrierName\") || name.includes(\"invoiceDate\") || name.includes(\"receivedDate\") || name.includes(\"ftpFileName\") || name.includes(\"company\") || name.includes(\"division\"))) {\n                const timeoutId = setTimeout(()=>{\n                    updateFilenames();\n                    if (name.includes(\"division\")) {\n                        const entryMatch = name.match(/entries\\.(\\d+)\\.division/);\n                        if (entryMatch) {\n                            var _formValues_entries, _clientOptions_find;\n                            const entryIndex = parseInt(entryMatch[1], 10);\n                            const formValues = form.getValues();\n                            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n                            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n                            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                            if (entryClientName === \"LEGRAND\") {\n                                var _formValues_entries_entryIndex, _formValues_entries1;\n                                const divisionValue = (_formValues_entries1 = formValues.entries) === null || _formValues_entries1 === void 0 ? void 0 : (_formValues_entries_entryIndex = _formValues_entries1[entryIndex]) === null || _formValues_entries_entryIndex === void 0 ? void 0 : _formValues_entries_entryIndex.division;\n                                if (divisionValue) {\n                                    handleManualMatchingAutoFill(entryIndex, divisionValue);\n                                }\n                            }\n                        }\n                    }\n                    if (name.includes(\"clientId\")) {\n                        const entryMatch = name.match(/entries\\.(\\d+)\\.clientId/);\n                        if (entryMatch) {\n                            var _formValues_entries2, _clientOptions_find1;\n                            const entryIndex = parseInt(entryMatch[1], 10);\n                            const formValues = form.getValues();\n                            const entry = (_formValues_entries2 = formValues.entries) === null || _formValues_entries2 === void 0 ? void 0 : _formValues_entries2[entryIndex];\n                            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find1 = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find1 === void 0 ? void 0 : _clientOptions_find1.name) || \"\";\n                            if (entryClientName === \"LEGRAND\" && (entry === null || entry === void 0 ? void 0 : entry.division)) {\n                                handleManualMatchingAutoFill(entryIndex, entry.division);\n                            }\n                        }\n                    }\n                }, 100);\n                return ()=>clearTimeout(timeoutId);\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        updateFilenames,\n        handleManualMatchingAutoFill,\n        clientOptions,\n        form\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (associateId) {\n            form.setValue(\"associateId\", associateId);\n        }\n    }, [\n        associateId,\n        form\n    ]);\n    const checkInvoiceExistence = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (invoice)=>{\n        if (!invoice || invoice.length < 3) return false;\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_5__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_6__.trackSheets_routes.GET_RECEIVED_DATES_BY_INVOICE, \"?invoice=\").concat(invoice));\n            /* eslint-disable */ console.log(...oo_oo(\"2922015453_1004_6_1009_7_4\", \"[checkInvoiceExistence] invoice:\", invoice, \"API response:\", response));\n            return Array.isArray(response) && response.length > 0;\n        } catch (error) {\n            /* eslint-disable */ console.error(...oo_tx(\"2922015453_1012_6_1012_60_11\", \"[checkInvoiceExistence] Error:\", error));\n            return false;\n        }\n    }, []);\n    const checkReceivedDateExistence = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (invoice, receivedDate)=>{\n        if (!invoice || !receivedDate || invoice.length < 3) return false;\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_5__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_6__.trackSheets_routes.GET_RECEIVED_DATES_BY_INVOICE, \"?invoice=\").concat(invoice));\n            /* eslint-disable */ console.log(...oo_oo(\"2922015453_1024_8_1031_9_4\", \"[checkReceivedDateExistence] invoice:\", invoice, \"receivedDate:\", receivedDate, \"API response:\", response));\n            if (Array.isArray(response) && response.length > 0) {\n                const [day, month, year] = receivedDate.split(\"/\");\n                const inputDate = new Date(Date.UTC(parseInt(year, 10), parseInt(month, 10) - 1, parseInt(day, 10)));\n                const inputDateISO = inputDate.toISOString().split(\"T\")[0];\n                const exists = response.some((item)=>{\n                    if (item.receivedDate) {\n                        const apiDate = new Date(item.receivedDate);\n                        const apiDateISO = apiDate.toISOString().split(\"T\")[0];\n                        return apiDateISO === inputDateISO;\n                    }\n                    return false;\n                });\n                /* eslint-disable */ console.log(...oo_oo(\"2922015453_1051_10_1051_69_4\", \"[checkReceivedDateExistence] exists:\", exists));\n                setExistingEntries((prev)=>({\n                        ...prev,\n                        [\"\".concat(invoice, \"-\").concat(receivedDate)]: exists\n                    }));\n                return exists;\n            }\n            return false;\n        } catch (error) {\n            /* eslint-disable */ console.error(...oo_tx(\"2922015453_1061_8_1061_61_11\", \"Error checking received date:\", error));\n            return false;\n        }\n    }, []);\n    const onSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (values, event)=>{\n        // Clear previous errors\n        for(let i = 0; i < values.entries.length; i++){\n            form.clearErrors(\"entries.\".concat(i, \".invoice\"));\n            form.clearErrors(\"entries.\".concat(i, \".receivedDate\"));\n        }\n        // Async duplicate checks for each entry\n        const errorsToApply = [];\n        const validationPromises = values.entries.map(async (entry, index)=>{\n            let entryHasError = false;\n            const invoiceExists = await checkInvoiceExistence(entry.invoice);\n            if (invoiceExists) {\n                errorsToApply.push({\n                    field: \"entries.\".concat(index, \".invoice\"),\n                    message: \"This invoice already exists\"\n                });\n                if (entry.invoice && entry.receivedDate) {\n                    const receivedDateExists = await checkReceivedDateExistence(entry.invoice, entry.receivedDate);\n                    if (receivedDateExists) {\n                        errorsToApply.push({\n                            field: \"entries.\".concat(index, \".receivedDate\"),\n                            message: \"This received date already exists for this invoice\"\n                        });\n                        entryHasError = true;\n                    } else {\n                        errorsToApply.push({\n                            field: \"entries.\".concat(index, \".receivedDate\"),\n                            message: \"Warning: Different received date for existing invoice\"\n                        });\n                    }\n                }\n            }\n            return {\n                isValid: !entryHasError\n            };\n        });\n        await Promise.all(validationPromises);\n        errorsToApply.forEach((param)=>{\n            let { field, message } = param;\n            form.setError(field, {\n                type: \"manual\",\n                message\n            });\n        });\n        const hasDuplicateReceivedDateErrors = errorsToApply.some((error)=>error.field.includes(\"receivedDate\") && error.message.includes(\"already exists\"));\n        if (hasDuplicateReceivedDateErrors) {\n            return;\n        }\n        // If all checks pass, proceed to submit\n        await (0,_utils_createTracksheetSubmit__WEBPACK_IMPORTED_MODULE_14__.createTracksheetSubmit)({\n            values,\n            form,\n            clientFilePathFormat,\n            generateFilename,\n            notify: (type, message)=>sonner__WEBPACK_IMPORTED_MODULE_8__.toast[type](message),\n            onSuccess: ()=>{\n                form.setValue(\"associateId\", associateId);\n            },\n            userData,\n            fetchCustomFieldsForClient\n        });\n    }, [\n        form,\n        clientFilePathFormat,\n        generateFilename,\n        associateId,\n        checkInvoiceExistence,\n        checkReceivedDateExistence,\n        userData\n    ]);\n    const handleFormKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.ctrlKey && (e.key === \"s\" || e.key === \"S\")) {\n            e.preventDefault();\n            form.handleSubmit(onSubmit)();\n        } else if (e.key === \"Enter\" && !e.ctrlKey && !e.shiftKey && !e.altKey) {\n            const activeElement = document.activeElement;\n            const isSubmitButton = (activeElement === null || activeElement === void 0 ? void 0 : activeElement.getAttribute(\"type\")) === \"submit\";\n            if (isSubmitButton) {\n                e.preventDefault();\n                form.handleSubmit(onSubmit)();\n            }\n        }\n    }, [\n        form,\n        onSubmit\n    ]);\n    const getFilteredDivisionOptions = (company, entryIndex)=>{\n        if (entryIndex !== undefined) {\n            var _formValues_entries, _clientOptions_find;\n            const formValues = form.getValues();\n            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n            if (entryClientName === \"LEGRAND\") {\n                const shipperAlias = form.getValues(\"entries.\".concat(entryIndex, \".shipperAlias\"));\n                const consigneeAlias = form.getValues(\"entries.\".concat(entryIndex, \".consigneeAlias\"));\n                const billtoAlias = form.getValues(\"entries.\".concat(entryIndex, \".billtoAlias\"));\n                const currentAlias = shipperAlias || consigneeAlias || billtoAlias;\n                if (currentAlias) {\n                    const selectedData = legrandData.find((data)=>{\n                        const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                        return uniqueKey === currentAlias;\n                    });\n                    if (selectedData) {\n                        const baseAliasName = selectedData.aliasShippingNames && selectedData.aliasShippingNames !== \"NONE\" ? selectedData.aliasShippingNames : selectedData.legalName;\n                        const sameAliasEntries = legrandData.filter((data)=>{\n                            const dataAliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : data.legalName;\n                            return dataAliasName === baseAliasName;\n                        });\n                        const allDivisions = [];\n                        sameAliasEntries.forEach((entry)=>{\n                            if (entry.customeCode) {\n                                if (entry.customeCode.includes(\"/\")) {\n                                    const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                    allDivisions.push(...splitDivisions);\n                                } else {\n                                    allDivisions.push(entry.customeCode);\n                                }\n                            }\n                        });\n                        const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                        if (uniqueDivisions.length > 0) {\n                            const contextDivisions = uniqueDivisions.sort().map((code)=>({\n                                    value: code,\n                                    label: code\n                                }));\n                            return contextDivisions;\n                        }\n                    }\n                }\n            }\n        }\n        return [];\n    };\n    const handleOpenImportModal = ()=>{\n        setImportModalOpen(true);\n    };\n    const handleCloseImportModal = ()=>{\n        setImportModalOpen(false);\n    };\n    const renderTooltipContent = (index)=>{\n        if (!clientFilePathFormat) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-medium mb-1\",\n                        children: [\n                            \"Entry #\",\n                            index + 1,\n                            \" Filename\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                        lineNumber: 1258,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-medium text-orange-600 mb-2\",\n                        children: \"Please select a client to generate filename\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                        lineNumber: 1259,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                lineNumber: 1257,\n                columnNumber: 9\n            }, undefined);\n        }\n        const hasGeneratedFilename = generatedFilenames[index] && generatedFilenames[index].length > 0;\n        const isValid = filenameValidation[index];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-sm max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"font-medium mb-1\",\n                    children: [\n                        \"Entry #\",\n                        index + 1,\n                        \" Filename\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                    lineNumber: 1272,\n                    columnNumber: 9\n                }, undefined),\n                hasGeneratedFilename && isValid ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium text-green-600 mb-2\",\n                            children: \"Filename Generated Successfully\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                            lineNumber: 1275,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs font-mono break-all bg-gray-100 p-2 rounded text-black\",\n                            children: generatedFilenames[index]\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                            lineNumber: 1278,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: [\n                                \"Pattern: \",\n                                clientFilePathFormat\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                            lineNumber: 1281,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                    lineNumber: 1274,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-medium text-orange-600 mb-1\",\n                            children: hasGeneratedFilename ? \"Invalid Filename\" : \"Please fill the form to generate filename\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                            lineNumber: 1287,\n                            columnNumber: 13\n                        }, undefined),\n                        missingFields[index] && missingFields[index].length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600 mb-2\",\n                                    children: \"Missing fields:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1294,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1\",\n                                    children: missingFields[index].map((field, fieldIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"text-xs\",\n                                            children: field\n                                        }, fieldIndex, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                                            lineNumber: 1297,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1295,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                    lineNumber: 1286,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n            lineNumber: 1271,\n            columnNumber: 7\n        }, undefined);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchAssignedFiles = async ()=>{\n            if (!(userData === null || userData === void 0 ? void 0 : userData.id)) return;\n            try {\n                const res = await fetch(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_6__.invoiceFile_routes.GET_INVOICE_FILES_BY_USER, \"/\").concat(userData.id));\n                const data = await res.json();\n                if (data.success && Array.isArray(data.data)) {\n                    setAssignedFiles(data.data);\n                } else {\n                    setAssignedFiles([]);\n                }\n            } catch (err) {\n                setAssignedFiles([]);\n            }\n        };\n        fetchAssignedFiles();\n    }, [\n        userData === null || userData === void 0 ? void 0 : userData.id\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_9__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-2 py-3\",\n                children: activeView === \"view\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        permissions: permissions,\n                        client: client,\n                        clientDataUpdate: clientDataUpdate,\n                        carrierDataUpdate: carrierDataUpdate\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                        lineNumber: 1336,\n                        columnNumber: 15\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                    lineNumber: 1335,\n                    columnNumber: 13\n                }, undefined) : showFullForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                    ...form,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: form.handleSubmit(onSubmit),\n                        className: \"space-y-3\",\n                        children: [\n                            entries.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TracksheetEntryForm__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    index: index,\n                                    form: form,\n                                    clientOptions: clientOptions,\n                                    carrierByClient: carrierByClient,\n                                    legrandData: legrandData,\n                                    handleFtpFileNameChange: handleFtpFileNameChange,\n                                    handleLegrandDataChange: handleLegrandDataChange,\n                                    getFilteredDivisionOptions: getFilteredDivisionOptions,\n                                    updateFilenames: updateFilenames,\n                                    clientFilePathFormat: clientFilePathFormat,\n                                    generatedFilenames: generatedFilenames,\n                                    filenameValidation: filenameValidation,\n                                    renderTooltipContent: renderTooltipContent,\n                                    checkInvoiceExistence: checkInvoiceExistence,\n                                    checkReceivedDateExistence: checkReceivedDateExistence,\n                                    validateDateFormat: validateDateFormat,\n                                    handleDateChange: handleDateChange,\n                                    legrandsData: legrandsData,\n                                    manualMatchingData: manualMatchingData,\n                                    handleManualMatchingAutoFill: handleManualMatchingAutoFill,\n                                    assignedFiles: assignedFiles\n                                }, index, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1351,\n                                    columnNumber: 21\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"submit\",\n                                    className: \"w-32\",\n                                    children: \"Save\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1380,\n                                    columnNumber: 21\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                                lineNumber: 1379,\n                                columnNumber: 19\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                        lineNumber: 1346,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                    lineNumber: 1345,\n                    columnNumber: 15\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n                lineNumber: 1333,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n            lineNumber: 1332,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTracksheet\\\\createTrackSheet.tsx\",\n        lineNumber: 1331,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateTrackSheet, \"f83RhDuewxLo4DX+w7hxrVUzgUk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_16__.useWatch,\n        _hooks_useFilenameGenerator__WEBPACK_IMPORTED_MODULE_13__.useFilenameGenerator,\n        _hooks_useTracksheetLogic__WEBPACK_IMPORTED_MODULE_12__.useTracksheetLogic\n    ];\n});\n_c = CreateTrackSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateTrackSheet); /* eslint-disable */ \nfunction oo_cm() {\n    try {\n        return (0, eval)(\"globalThis._console_ninja\") || (0, eval)(\"/* https://github.com/wallabyjs/console-ninja#how-does-it-work */'use strict';var _0x2521ed=_0x22d4;(function(_0x15e824,_0x52e4de){var _0x3fcd51=_0x22d4,_0x1510e4=_0x15e824();while(!![]){try{var _0x33050d=parseInt(_0x3fcd51(0x127))/0x1*(parseInt(_0x3fcd51(0xb5))/0x2)+parseInt(_0x3fcd51(0x11c))/0x3*(-parseInt(_0x3fcd51(0x180))/0x4)+parseInt(_0x3fcd51(0xd2))/0x5+parseInt(_0x3fcd51(0x151))/0x6*(-parseInt(_0x3fcd51(0x164))/0x7)+parseInt(_0x3fcd51(0x161))/0x8+-parseInt(_0x3fcd51(0xd3))/0x9*(parseInt(_0x3fcd51(0x108))/0xa)+parseInt(_0x3fcd51(0x129))/0xb;if(_0x33050d===_0x52e4de)break;else _0x1510e4['push'](_0x1510e4['shift']());}catch(_0x585a27){_0x1510e4['push'](_0x1510e4['shift']());}}}(_0x1bae,0xde4fd));var G=Object[_0x2521ed(0x177)],V=Object['defineProperty'],ee=Object[_0x2521ed(0xd8)],te=Object[_0x2521ed(0x15e)],ne=Object['getPrototypeOf'],re=Object['prototype'][_0x2521ed(0x94)],ie=(_0x15a493,_0x1da4e5,_0x38e998,_0x191789)=>{var _0x5aa4da=_0x2521ed;if(_0x1da4e5&&typeof _0x1da4e5==_0x5aa4da(0x110)||typeof _0x1da4e5==_0x5aa4da(0x144)){for(let _0x1898c9 of te(_0x1da4e5))!re[_0x5aa4da(0xe1)](_0x15a493,_0x1898c9)&&_0x1898c9!==_0x38e998&&V(_0x15a493,_0x1898c9,{'get':()=>_0x1da4e5[_0x1898c9],'enumerable':!(_0x191789=ee(_0x1da4e5,_0x1898c9))||_0x191789[_0x5aa4da(0xa8)]});}return _0x15a493;},j=(_0x34eccf,_0x3d547c,_0x129665)=>(_0x129665=_0x34eccf!=null?G(ne(_0x34eccf)):{},ie(_0x3d547c||!_0x34eccf||!_0x34eccf[_0x2521ed(0x160)]?V(_0x129665,_0x2521ed(0x106),{'value':_0x34eccf,'enumerable':!0x0}):_0x129665,_0x34eccf)),q=class{constructor(_0x361ce0,_0xcdc76,_0x1861eb,_0x135c38,_0x56d085,_0x24e2d1){var _0x4a32cf=_0x2521ed,_0x36205b,_0x176b2e,_0x1e4531,_0x247653;this[_0x4a32cf(0x167)]=_0x361ce0,this[_0x4a32cf(0xea)]=_0xcdc76,this[_0x4a32cf(0x189)]=_0x1861eb,this[_0x4a32cf(0x13b)]=_0x135c38,this[_0x4a32cf(0x9d)]=_0x56d085,this[_0x4a32cf(0x14f)]=_0x24e2d1,this[_0x4a32cf(0x18c)]=!0x0,this[_0x4a32cf(0x185)]=!0x0,this['_connected']=!0x1,this['_connecting']=!0x1,this[_0x4a32cf(0xf1)]=((_0x176b2e=(_0x36205b=_0x361ce0[_0x4a32cf(0x150)])==null?void 0x0:_0x36205b[_0x4a32cf(0x113)])==null?void 0x0:_0x176b2e['NEXT_RUNTIME'])===_0x4a32cf(0x107),this[_0x4a32cf(0x114)]=!((_0x247653=(_0x1e4531=this[_0x4a32cf(0x167)][_0x4a32cf(0x150)])==null?void 0x0:_0x1e4531['versions'])!=null&&_0x247653[_0x4a32cf(0x182)])&&!this[_0x4a32cf(0xf1)],this[_0x4a32cf(0x13c)]=null,this['_connectAttemptCount']=0x0,this['_maxConnectAttemptCount']=0x14,this[_0x4a32cf(0x138)]=_0x4a32cf(0x90),this[_0x4a32cf(0x17d)]=(this[_0x4a32cf(0x114)]?_0x4a32cf(0xcb):_0x4a32cf(0x13f))+this[_0x4a32cf(0x138)];}async['getWebSocketClass'](){var _0x3c3a4a=_0x2521ed,_0x3f7c0f,_0x407d5a;if(this[_0x3c3a4a(0x13c)])return this[_0x3c3a4a(0x13c)];let _0x261630;if(this['_inBrowser']||this[_0x3c3a4a(0xf1)])_0x261630=this[_0x3c3a4a(0x167)][_0x3c3a4a(0xe7)];else{if((_0x3f7c0f=this[_0x3c3a4a(0x167)][_0x3c3a4a(0x150)])!=null&&_0x3f7c0f[_0x3c3a4a(0x11a)])_0x261630=(_0x407d5a=this['global']['process'])==null?void 0x0:_0x407d5a[_0x3c3a4a(0x11a)];else try{let _0x1bc4c6=await import(_0x3c3a4a(0x16c));_0x261630=(await import((await import(_0x3c3a4a(0xdf)))[_0x3c3a4a(0xc4)](_0x1bc4c6[_0x3c3a4a(0x148)](this[_0x3c3a4a(0x13b)],_0x3c3a4a(0x12f)))[_0x3c3a4a(0x12a)]()))[_0x3c3a4a(0x106)];}catch{try{_0x261630=require(require('path')[_0x3c3a4a(0x148)](this[_0x3c3a4a(0x13b)],'ws'));}catch{throw new Error(_0x3c3a4a(0xa0));}}}return this['_WebSocketClass']=_0x261630,_0x261630;}['_connectToHostNow'](){var _0x24affd=_0x2521ed;this[_0x24affd(0xd4)]||this[_0x24affd(0xb3)]||this[_0x24affd(0x166)]>=this[_0x24affd(0xb2)]||(this['_allowedToConnectOnSend']=!0x1,this['_connecting']=!0x0,this[_0x24affd(0x166)]++,this[_0x24affd(0xaa)]=new Promise((_0x1c13ba,_0x542a06)=>{var _0x3c2948=_0x24affd;this[_0x3c2948(0x16d)]()[_0x3c2948(0x121)](_0x14d8ea=>{var _0x93c003=_0x3c2948;let _0x375890=new _0x14d8ea(_0x93c003(0x12b)+(!this[_0x93c003(0x114)]&&this[_0x93c003(0x9d)]?_0x93c003(0x18d):this[_0x93c003(0xea)])+':'+this[_0x93c003(0x189)]);_0x375890[_0x93c003(0x128)]=()=>{var _0x5756af=_0x93c003;this['_allowedToSend']=!0x1,this[_0x5756af(0x171)](_0x375890),this[_0x5756af(0x10c)](),_0x542a06(new Error('logger\\\\x20websocket\\\\x20error'));},_0x375890[_0x93c003(0xc6)]=()=>{var _0x49cc48=_0x93c003;this[_0x49cc48(0x114)]||_0x375890[_0x49cc48(0x140)]&&_0x375890[_0x49cc48(0x140)]['unref']&&_0x375890[_0x49cc48(0x140)][_0x49cc48(0xf3)](),_0x1c13ba(_0x375890);},_0x375890[_0x93c003(0x187)]=()=>{var _0x23463a=_0x93c003;this[_0x23463a(0x185)]=!0x0,this[_0x23463a(0x171)](_0x375890),this['_attemptToReconnectShortly']();},_0x375890[_0x93c003(0xc9)]=_0x75a52e=>{var _0x3951ee=_0x93c003;try{if(!(_0x75a52e!=null&&_0x75a52e[_0x3951ee(0xa3)])||!this[_0x3951ee(0x14f)])return;let _0x78d515=JSON[_0x3951ee(0x111)](_0x75a52e[_0x3951ee(0xa3)]);this[_0x3951ee(0x14f)](_0x78d515[_0x3951ee(0xec)],_0x78d515[_0x3951ee(0x157)],this['global'],this[_0x3951ee(0x114)]);}catch{}};})[_0x3c2948(0x121)](_0x51dfb1=>(this['_connected']=!0x0,this[_0x3c2948(0xd4)]=!0x1,this[_0x3c2948(0x185)]=!0x1,this[_0x3c2948(0x18c)]=!0x0,this['_connectAttemptCount']=0x0,_0x51dfb1))[_0x3c2948(0xc0)](_0xb4565c=>(this[_0x3c2948(0xb3)]=!0x1,this[_0x3c2948(0xd4)]=!0x1,console[_0x3c2948(0x184)](_0x3c2948(0xb4)+this[_0x3c2948(0x138)]),_0x542a06(new Error(_0x3c2948(0xdc)+(_0xb4565c&&_0xb4565c['message'])))));}));}[_0x2521ed(0x171)](_0x2bbff1){var _0x7a7224=_0x2521ed;this[_0x7a7224(0xb3)]=!0x1,this[_0x7a7224(0xd4)]=!0x1;try{_0x2bbff1['onclose']=null,_0x2bbff1[_0x7a7224(0x128)]=null,_0x2bbff1['onopen']=null;}catch{}try{_0x2bbff1[_0x7a7224(0xc2)]<0x2&&_0x2bbff1[_0x7a7224(0x116)]();}catch{}}[_0x2521ed(0x10c)](){var _0x822240=_0x2521ed;clearTimeout(this[_0x822240(0x181)]),!(this[_0x822240(0x166)]>=this[_0x822240(0xb2)])&&(this['_reconnectTimeout']=setTimeout(()=>{var _0x572ab4=_0x822240,_0x59a299;this[_0x572ab4(0xb3)]||this['_connecting']||(this[_0x572ab4(0x16e)](),(_0x59a299=this[_0x572ab4(0xaa)])==null||_0x59a299[_0x572ab4(0xc0)](()=>this[_0x572ab4(0x10c)]()));},0x1f4),this[_0x822240(0x181)]['unref']&&this[_0x822240(0x181)][_0x822240(0xf3)]());}async[_0x2521ed(0xbd)](_0x4b15cd){var _0x362d6e=_0x2521ed;try{if(!this['_allowedToSend'])return;this[_0x362d6e(0x185)]&&this[_0x362d6e(0x16e)](),(await this['_ws'])[_0x362d6e(0xbd)](JSON[_0x362d6e(0xd5)](_0x4b15cd));}catch(_0x152473){this[_0x362d6e(0xbf)]?console[_0x362d6e(0x184)](this[_0x362d6e(0x17d)]+':\\\\x20'+(_0x152473&&_0x152473['message'])):(this[_0x362d6e(0xbf)]=!0x0,console[_0x362d6e(0x184)](this['_sendErrorMessage']+':\\\\x20'+(_0x152473&&_0x152473[_0x362d6e(0x12d)]),_0x4b15cd)),this['_allowedToSend']=!0x1,this[_0x362d6e(0x10c)]();}}};function _0x1bae(){var _0x475517=['unknown','_addFunctionsNode','failed\\\\x20to\\\\x20find\\\\x20and\\\\x20load\\\\x20WebSocket','replace','parent','data','[object\\\\x20Set]','autoExpandPreviousObjects','_setNodeExpressionPath','value','enumerable','autoExpandPropertyCount','_ws','_setNodeQueryPath','next.js','trace','_p_length','depth','58675','push','_maxConnectAttemptCount','_connected','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host,\\\\x20see\\\\x20','163798kPEYdI','expressionsToEvaluate','string','current','noFunctions','_addProperty','_additionalMetadata','funcName','send','_p_name','_extendedWarning','catch','_consoleNinjaAllowedToStart','readyState',[\\\"localhost\\\",\\\"127.0.0.1\\\",\\\"example.cypress.io\\\",\\\"DESKTOP-QUQRE9R\\\",\\\"***************\\\"],'pathToFileURL','bigint','onopen','Boolean','_treeNodePropertiesBeforeFullValue','onmessage','[object\\\\x20Array]','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20refreshing\\\\x20the\\\\x20page\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','console','elements','_getOwnPropertySymbols','totalStrLength','_isArray','Map','7861370iASdlg','2079585kwXuMV','_connecting','stringify','_propertyName','log','getOwnPropertyDescriptor','RegExp','_treeNodePropertiesAfterFullValue','resolveGetters','failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host:\\\\x20','hrtime','props','url','level','call','map','_isPrimitiveWrapperType','_blacklistedProperty','[object\\\\x20BigInt]','match','WebSocket','serialize','autoExpandMaxDepth','host','strLength','method','_isMap','Number','unshift','versions','_inNextEdge','_Symbol','unref','_isPrimitiveType','length','_numberRegExp','_setNodeId','_console_ninja','constructor','_console_ninja_session','rootExpression',\\\"c:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\.vscode\\\\\\\\extensions\\\\\\\\wallabyjs.console-ninja-1.0.460\\\\\\\\node_modules\\\",'angular','null','slice','Symbol','bind','index','array','pop','boolean','default','edge','10jggNHF','astro','_p_','NEXT_RUNTIME','_attemptToReconnectShortly','capped','','127.0.0.1','object','parse','_setNodeExpandableState','env','_inBrowser','allStrLength','close','remix','_isNegativeZero','disabledLog','_WebSocket','origin','24jBCuxm','date','see\\\\x20https://tinyurl.com/2vt8jxzw\\\\x20for\\\\x20more\\\\x20info.','count','timeStamp','then','_addObjectProperty','NEGATIVE_INFINITY','_addLoadNode','nan','symbol','1lYcQod','onerror','13530121SEjCVL','toString','ws://','next.js','message','location','ws/index.js','_regExpToString','setter','[object\\\\x20Map]','_keyStrRegExp','isExpressionToEvaluate','%c\\\\x20Console\\\\x20Ninja\\\\x20extension\\\\x20is\\\\x20connected\\\\x20to\\\\x20','Error','time','_webSocketErrorDocsLink','includes','root_exp','nodeModules','_WebSocketClass','get','coverage','Console\\\\x20Ninja\\\\x20failed\\\\x20to\\\\x20send\\\\x20logs,\\\\x20restarting\\\\x20the\\\\x20process\\\\x20may\\\\x20help;\\\\x20also\\\\x20see\\\\x20','_socket','prototype','reduceLimits','_dateToString','function','_isSet','String','_objectToString','join','_hasMapOnItsPath','_setNodeLabel','reload','substr','endsWith','autoExpandLimit','eventReceivedCallback','process','4201998ehWVyE','performance','stackTraceLimit','test','set','autoExpand','args','perf_hooks','_getOwnPropertyDescriptor','_capIfString','_getOwnPropertyNames','valueOf','now','getOwnPropertyNames','...','__es'+'Module','2697880RnkQZv','type','stack','7ePNIPb','fromCharCode','_connectAttemptCount','global','negativeZero','_ninjaIgnoreNextError','error','_sortProps','path','getWebSocketClass','_connectToHostNow','_setNodePermissions','_undefined','_disposeWebsocket','cappedProps','negativeInfinity','sortProps','HTMLAllCollection','indexOf','create','_hasSymbolPropertyOnItsPath','charAt','hits','name','_cleanNode','_sendErrorMessage','_property','_type','689716KYriyM','_reconnectTimeout','node','getter','warn','_allowedToConnectOnSend','Set','onclose','number','port','root_exp_id','getOwnPropertySymbols','_allowedToSend','gateway.docker.internal','disabledTrace','undefined','https://tinyurl.com/37x8b79t','expId','forEach','_processTreeNodeResult','hasOwnProperty','logger\\\\x20failed\\\\x20to\\\\x20connect\\\\x20to\\\\x20host','POSITIVE_INFINITY','isArray','[object\\\\x20Date]','_HTMLAllCollection','concat','hostname','\\\\x20browser','dockerizedApp'];_0x1bae=function(){return _0x475517;};return _0x1bae();}function H(_0x5ac5d3,_0x5a024c,_0x41428f,_0x2b7c5f,_0x32bd1c,_0x410870,_0x173eb4,_0x411518=oe){var _0x35ca23=_0x2521ed;let _0x2973b8=_0x41428f['split'](',')['map'](_0x4ce34d=>{var _0x54e87f=_0x22d4,_0x4bc88c,_0x1896c3,_0x4bf3fc,_0x1b5e14;try{if(!_0x5ac5d3[_0x54e87f(0xfa)]){let _0x2f2f7f=((_0x1896c3=(_0x4bc88c=_0x5ac5d3[_0x54e87f(0x150)])==null?void 0x0:_0x4bc88c[_0x54e87f(0xf0)])==null?void 0x0:_0x1896c3['node'])||((_0x1b5e14=(_0x4bf3fc=_0x5ac5d3[_0x54e87f(0x150)])==null?void 0x0:_0x4bf3fc['env'])==null?void 0x0:_0x1b5e14[_0x54e87f(0x10b)])==='edge';(_0x32bd1c===_0x54e87f(0xac)||_0x32bd1c===_0x54e87f(0x117)||_0x32bd1c===_0x54e87f(0x109)||_0x32bd1c===_0x54e87f(0xfd))&&(_0x32bd1c+=_0x2f2f7f?'\\\\x20server':_0x54e87f(0x9c)),_0x5ac5d3[_0x54e87f(0xfa)]={'id':+new Date(),'tool':_0x32bd1c},_0x173eb4&&_0x32bd1c&&!_0x2f2f7f&&console[_0x54e87f(0xd7)](_0x54e87f(0x135)+(_0x32bd1c[_0x54e87f(0x179)](0x0)['toUpperCase']()+_0x32bd1c[_0x54e87f(0x14c)](0x1))+',','background:\\\\x20rgb(30,30,30);\\\\x20color:\\\\x20rgb(255,213,92)',_0x54e87f(0x11e));}let _0x38266e=new q(_0x5ac5d3,_0x5a024c,_0x4ce34d,_0x2b7c5f,_0x410870,_0x411518);return _0x38266e[_0x54e87f(0xbd)][_0x54e87f(0x101)](_0x38266e);}catch(_0x605813){return console[_0x54e87f(0x184)](_0x54e87f(0x95),_0x605813&&_0x605813[_0x54e87f(0x12d)]),()=>{};}});return _0x2cbe92=>_0x2973b8[_0x35ca23(0x92)](_0x4df820=>_0x4df820(_0x2cbe92));}function oe(_0x4723a9,_0xdcafa3,_0x18cb5f,_0x306248){var _0x7974a6=_0x2521ed;_0x306248&&_0x4723a9===_0x7974a6(0x14b)&&_0x18cb5f[_0x7974a6(0x12e)][_0x7974a6(0x14b)]();}function B(_0x49df36){var _0x8c4455=_0x2521ed,_0x1aef62,_0x33ca0f;let _0x504d4d=function(_0x3e4d21,_0x3119a9){return _0x3119a9-_0x3e4d21;},_0x4e68dc;if(_0x49df36[_0x8c4455(0x152)])_0x4e68dc=function(){return _0x49df36['performance']['now']();};else{if(_0x49df36[_0x8c4455(0x150)]&&_0x49df36[_0x8c4455(0x150)]['hrtime']&&((_0x33ca0f=(_0x1aef62=_0x49df36['process'])==null?void 0x0:_0x1aef62[_0x8c4455(0x113)])==null?void 0x0:_0x33ca0f[_0x8c4455(0x10b)])!==_0x8c4455(0x107))_0x4e68dc=function(){var _0x1f5058=_0x8c4455;return _0x49df36[_0x1f5058(0x150)][_0x1f5058(0xdd)]();},_0x504d4d=function(_0x4e233c,_0x163bff){return 0x3e8*(_0x163bff[0x0]-_0x4e233c[0x0])+(_0x163bff[0x1]-_0x4e233c[0x1])/0xf4240;};else try{let {performance:_0x92d690}=require(_0x8c4455(0x158));_0x4e68dc=function(){return _0x92d690['now']();};}catch{_0x4e68dc=function(){return+new Date();};}}return{'elapsed':_0x504d4d,'timeStamp':_0x4e68dc,'now':()=>Date[_0x8c4455(0x15d)]()};}function _0x22d4(_0x12edb4,_0x271789){var _0x1bae16=_0x1bae();return _0x22d4=function(_0x22d4de,_0x5d435d){_0x22d4de=_0x22d4de-0x8f;var _0x5cf399=_0x1bae16[_0x22d4de];return _0x5cf399;},_0x22d4(_0x12edb4,_0x271789);}function X(_0x353b15,_0x1aa730,_0x1de36e){var _0x290fd=_0x2521ed,_0x2cd4ae,_0x424619,_0x4f2a6c,_0x1d9986,_0x230056;if(_0x353b15[_0x290fd(0xc1)]!==void 0x0)return _0x353b15[_0x290fd(0xc1)];let _0x542a02=((_0x424619=(_0x2cd4ae=_0x353b15['process'])==null?void 0x0:_0x2cd4ae[_0x290fd(0xf0)])==null?void 0x0:_0x424619[_0x290fd(0x182)])||((_0x1d9986=(_0x4f2a6c=_0x353b15[_0x290fd(0x150)])==null?void 0x0:_0x4f2a6c[_0x290fd(0x113)])==null?void 0x0:_0x1d9986[_0x290fd(0x10b)])==='edge';function _0x394a2b(_0x45bd35){var _0x5eb749=_0x290fd;if(_0x45bd35['startsWith']('/')&&_0x45bd35[_0x5eb749(0x14d)]('/')){let _0x21a10f=new RegExp(_0x45bd35[_0x5eb749(0xff)](0x1,-0x1));return _0xe0fca5=>_0x21a10f[_0x5eb749(0x154)](_0xe0fca5);}else{if(_0x45bd35[_0x5eb749(0x139)]('*')||_0x45bd35[_0x5eb749(0x139)]('?')){let _0x95c811=new RegExp('^'+_0x45bd35['replace'](/\\\\./g,String[_0x5eb749(0x165)](0x5c)+'.')['replace'](/\\\\*/g,'.*')['replace'](/\\\\?/g,'.')+String[_0x5eb749(0x165)](0x24));return _0x40ec94=>_0x95c811[_0x5eb749(0x154)](_0x40ec94);}else return _0x3f24dd=>_0x3f24dd===_0x45bd35;}}let _0x2ca7df=_0x1aa730[_0x290fd(0xe2)](_0x394a2b);return _0x353b15[_0x290fd(0xc1)]=_0x542a02||!_0x1aa730,!_0x353b15[_0x290fd(0xc1)]&&((_0x230056=_0x353b15[_0x290fd(0x12e)])==null?void 0x0:_0x230056['hostname'])&&(_0x353b15[_0x290fd(0xc1)]=_0x2ca7df['some'](_0x23fe2b=>_0x23fe2b(_0x353b15[_0x290fd(0x12e)][_0x290fd(0x9b)]))),_0x353b15['_consoleNinjaAllowedToStart'];}function J(_0x1c6eb0,_0x552919,_0x51de18,_0x533c9c){var _0xd5a59d=_0x2521ed;_0x1c6eb0=_0x1c6eb0,_0x552919=_0x552919,_0x51de18=_0x51de18,_0x533c9c=_0x533c9c;let _0x1136ef=B(_0x1c6eb0),_0x1a1b80=_0x1136ef['elapsed'],_0x57d945=_0x1136ef[_0xd5a59d(0x120)];class _0x4a2137{constructor(){var _0x20a872=_0xd5a59d;this[_0x20a872(0x133)]=/^(?!(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$)[_$a-zA-Z\\\\xA0-\\\\uFFFF][_$a-zA-Z0-9\\\\xA0-\\\\uFFFF]*$/,this[_0x20a872(0xf6)]=/^(0|[1-9][0-9]*)$/,this['_quotedRegExp']=/'([^\\\\\\\\']|\\\\\\\\')*'/,this[_0x20a872(0x170)]=_0x1c6eb0[_0x20a872(0x8f)],this[_0x20a872(0x99)]=_0x1c6eb0['HTMLAllCollection'],this[_0x20a872(0x159)]=Object[_0x20a872(0xd8)],this[_0x20a872(0x15b)]=Object[_0x20a872(0x15e)],this[_0x20a872(0xf2)]=_0x1c6eb0[_0x20a872(0x100)],this[_0x20a872(0x130)]=RegExp[_0x20a872(0x141)][_0x20a872(0x12a)],this['_dateToString']=Date[_0x20a872(0x141)][_0x20a872(0x12a)];}[_0xd5a59d(0xe8)](_0x19e7d2,_0xdeff97,_0x1b41e0,_0x5a8d18){var _0x369a40=_0xd5a59d,_0x4e0a0b=this,_0x207418=_0x1b41e0[_0x369a40(0x156)];function _0x356ab7(_0x1764a0,_0x28cb69,_0x5cd086){var _0x2e85e4=_0x369a40;_0x28cb69[_0x2e85e4(0x162)]=_0x2e85e4(0x9e),_0x28cb69[_0x2e85e4(0x16a)]=_0x1764a0[_0x2e85e4(0x12d)],_0x1f9073=_0x5cd086[_0x2e85e4(0x182)][_0x2e85e4(0xb8)],_0x5cd086[_0x2e85e4(0x182)][_0x2e85e4(0xb8)]=_0x28cb69,_0x4e0a0b[_0x2e85e4(0xc8)](_0x28cb69,_0x5cd086);}let _0x371ae5;_0x1c6eb0[_0x369a40(0xcc)]&&(_0x371ae5=_0x1c6eb0[_0x369a40(0xcc)][_0x369a40(0x16a)],_0x371ae5&&(_0x1c6eb0['console'][_0x369a40(0x16a)]=function(){}));try{try{_0x1b41e0['level']++,_0x1b41e0[_0x369a40(0x156)]&&_0x1b41e0['autoExpandPreviousObjects'][_0x369a40(0xb1)](_0xdeff97);var _0x1b2cf8,_0x5634a8,_0x24aca3,_0x524893,_0x2a7853=[],_0x3725f9=[],_0x1b95d6,_0x5f3857=this[_0x369a40(0x17f)](_0xdeff97),_0x40dc89=_0x5f3857===_0x369a40(0x103),_0x3a0f81=!0x1,_0x3e6606=_0x5f3857===_0x369a40(0x144),_0x12c80b=this[_0x369a40(0xf4)](_0x5f3857),_0x400804=this[_0x369a40(0xe3)](_0x5f3857),_0x4a9776=_0x12c80b||_0x400804,_0x5335ea={},_0x3502e7=0x0,_0x2885e3=!0x1,_0x1f9073,_0x318e18=/^(([1-9]{1}[0-9]*)|0)$/;if(_0x1b41e0[_0x369a40(0xaf)]){if(_0x40dc89){if(_0x5634a8=_0xdeff97[_0x369a40(0xf5)],_0x5634a8>_0x1b41e0['elements']){for(_0x24aca3=0x0,_0x524893=_0x1b41e0['elements'],_0x1b2cf8=_0x24aca3;_0x1b2cf8<_0x524893;_0x1b2cf8++)_0x3725f9['push'](_0x4e0a0b[_0x369a40(0xba)](_0x2a7853,_0xdeff97,_0x5f3857,_0x1b2cf8,_0x1b41e0));_0x19e7d2['cappedElements']=!0x0;}else{for(_0x24aca3=0x0,_0x524893=_0x5634a8,_0x1b2cf8=_0x24aca3;_0x1b2cf8<_0x524893;_0x1b2cf8++)_0x3725f9[_0x369a40(0xb1)](_0x4e0a0b[_0x369a40(0xba)](_0x2a7853,_0xdeff97,_0x5f3857,_0x1b2cf8,_0x1b41e0));}_0x1b41e0['autoExpandPropertyCount']+=_0x3725f9[_0x369a40(0xf5)];}if(!(_0x5f3857==='null'||_0x5f3857===_0x369a40(0x8f))&&!_0x12c80b&&_0x5f3857!==_0x369a40(0x146)&&_0x5f3857!=='Buffer'&&_0x5f3857!=='bigint'){var _0x9b164d=_0x5a8d18[_0x369a40(0xde)]||_0x1b41e0[_0x369a40(0xde)];if(this['_isSet'](_0xdeff97)?(_0x1b2cf8=0x0,_0xdeff97[_0x369a40(0x92)](function(_0x2e4c83){var _0xc34cf5=_0x369a40;if(_0x3502e7++,_0x1b41e0['autoExpandPropertyCount']++,_0x3502e7>_0x9b164d){_0x2885e3=!0x0;return;}if(!_0x1b41e0['isExpressionToEvaluate']&&_0x1b41e0[_0xc34cf5(0x156)]&&_0x1b41e0[_0xc34cf5(0xa9)]>_0x1b41e0[_0xc34cf5(0x14e)]){_0x2885e3=!0x0;return;}_0x3725f9[_0xc34cf5(0xb1)](_0x4e0a0b[_0xc34cf5(0xba)](_0x2a7853,_0xdeff97,_0xc34cf5(0x186),_0x1b2cf8++,_0x1b41e0,function(_0x29d796){return function(){return _0x29d796;};}(_0x2e4c83)));})):this[_0x369a40(0xed)](_0xdeff97)&&_0xdeff97[_0x369a40(0x92)](function(_0xfea0fe,_0xa14803){var _0x32aa72=_0x369a40;if(_0x3502e7++,_0x1b41e0[_0x32aa72(0xa9)]++,_0x3502e7>_0x9b164d){_0x2885e3=!0x0;return;}if(!_0x1b41e0['isExpressionToEvaluate']&&_0x1b41e0[_0x32aa72(0x156)]&&_0x1b41e0['autoExpandPropertyCount']>_0x1b41e0['autoExpandLimit']){_0x2885e3=!0x0;return;}var _0x3e871d=_0xa14803['toString']();_0x3e871d['length']>0x64&&(_0x3e871d=_0x3e871d[_0x32aa72(0xff)](0x0,0x64)+_0x32aa72(0x15f)),_0x3725f9[_0x32aa72(0xb1)](_0x4e0a0b[_0x32aa72(0xba)](_0x2a7853,_0xdeff97,_0x32aa72(0xd1),_0x3e871d,_0x1b41e0,function(_0x4b9377){return function(){return _0x4b9377;};}(_0xfea0fe)));}),!_0x3a0f81){try{for(_0x1b95d6 in _0xdeff97)if(!(_0x40dc89&&_0x318e18[_0x369a40(0x154)](_0x1b95d6))&&!this[_0x369a40(0xe4)](_0xdeff97,_0x1b95d6,_0x1b41e0)){if(_0x3502e7++,_0x1b41e0[_0x369a40(0xa9)]++,_0x3502e7>_0x9b164d){_0x2885e3=!0x0;break;}if(!_0x1b41e0[_0x369a40(0x134)]&&_0x1b41e0[_0x369a40(0x156)]&&_0x1b41e0[_0x369a40(0xa9)]>_0x1b41e0['autoExpandLimit']){_0x2885e3=!0x0;break;}_0x3725f9[_0x369a40(0xb1)](_0x4e0a0b['_addObjectProperty'](_0x2a7853,_0x5335ea,_0xdeff97,_0x5f3857,_0x1b95d6,_0x1b41e0));}}catch{}if(_0x5335ea[_0x369a40(0xae)]=!0x0,_0x3e6606&&(_0x5335ea[_0x369a40(0xbe)]=!0x0),!_0x2885e3){var _0x3b6b3a=[][_0x369a40(0x9a)](this[_0x369a40(0x15b)](_0xdeff97))['concat'](this[_0x369a40(0xce)](_0xdeff97));for(_0x1b2cf8=0x0,_0x5634a8=_0x3b6b3a[_0x369a40(0xf5)];_0x1b2cf8<_0x5634a8;_0x1b2cf8++)if(_0x1b95d6=_0x3b6b3a[_0x1b2cf8],!(_0x40dc89&&_0x318e18[_0x369a40(0x154)](_0x1b95d6['toString']()))&&!this[_0x369a40(0xe4)](_0xdeff97,_0x1b95d6,_0x1b41e0)&&!_0x5335ea[_0x369a40(0x10a)+_0x1b95d6[_0x369a40(0x12a)]()]){if(_0x3502e7++,_0x1b41e0[_0x369a40(0xa9)]++,_0x3502e7>_0x9b164d){_0x2885e3=!0x0;break;}if(!_0x1b41e0[_0x369a40(0x134)]&&_0x1b41e0[_0x369a40(0x156)]&&_0x1b41e0[_0x369a40(0xa9)]>_0x1b41e0[_0x369a40(0x14e)]){_0x2885e3=!0x0;break;}_0x3725f9[_0x369a40(0xb1)](_0x4e0a0b[_0x369a40(0x122)](_0x2a7853,_0x5335ea,_0xdeff97,_0x5f3857,_0x1b95d6,_0x1b41e0));}}}}}if(_0x19e7d2['type']=_0x5f3857,_0x4a9776?(_0x19e7d2['value']=_0xdeff97[_0x369a40(0x15c)](),this[_0x369a40(0x15a)](_0x5f3857,_0x19e7d2,_0x1b41e0,_0x5a8d18)):_0x5f3857===_0x369a40(0x11d)?_0x19e7d2[_0x369a40(0xa7)]=this[_0x369a40(0x143)][_0x369a40(0xe1)](_0xdeff97):_0x5f3857===_0x369a40(0xc5)?_0x19e7d2[_0x369a40(0xa7)]=_0xdeff97[_0x369a40(0x12a)]():_0x5f3857===_0x369a40(0xd9)?_0x19e7d2[_0x369a40(0xa7)]=this[_0x369a40(0x130)]['call'](_0xdeff97):_0x5f3857==='symbol'&&this['_Symbol']?_0x19e7d2['value']=this[_0x369a40(0xf2)][_0x369a40(0x141)][_0x369a40(0x12a)][_0x369a40(0xe1)](_0xdeff97):!_0x1b41e0[_0x369a40(0xaf)]&&!(_0x5f3857===_0x369a40(0xfe)||_0x5f3857==='undefined')&&(delete _0x19e7d2[_0x369a40(0xa7)],_0x19e7d2['capped']=!0x0),_0x2885e3&&(_0x19e7d2[_0x369a40(0x172)]=!0x0),_0x1f9073=_0x1b41e0[_0x369a40(0x182)][_0x369a40(0xb8)],_0x1b41e0[_0x369a40(0x182)][_0x369a40(0xb8)]=_0x19e7d2,this['_treeNodePropertiesBeforeFullValue'](_0x19e7d2,_0x1b41e0),_0x3725f9['length']){for(_0x1b2cf8=0x0,_0x5634a8=_0x3725f9['length'];_0x1b2cf8<_0x5634a8;_0x1b2cf8++)_0x3725f9[_0x1b2cf8](_0x1b2cf8);}_0x2a7853['length']&&(_0x19e7d2['props']=_0x2a7853);}catch(_0x43255c){_0x356ab7(_0x43255c,_0x19e7d2,_0x1b41e0);}this[_0x369a40(0xbb)](_0xdeff97,_0x19e7d2),this['_treeNodePropertiesAfterFullValue'](_0x19e7d2,_0x1b41e0),_0x1b41e0[_0x369a40(0x182)][_0x369a40(0xb8)]=_0x1f9073,_0x1b41e0[_0x369a40(0xe0)]--,_0x1b41e0[_0x369a40(0x156)]=_0x207418,_0x1b41e0[_0x369a40(0x156)]&&_0x1b41e0['autoExpandPreviousObjects'][_0x369a40(0x104)]();}finally{_0x371ae5&&(_0x1c6eb0['console'][_0x369a40(0x16a)]=_0x371ae5);}return _0x19e7d2;}['_getOwnPropertySymbols'](_0x47571d){var _0x1b79d4=_0xd5a59d;return Object[_0x1b79d4(0x18b)]?Object[_0x1b79d4(0x18b)](_0x47571d):[];}[_0xd5a59d(0x145)](_0x2365a1){var _0x95c97b=_0xd5a59d;return!!(_0x2365a1&&_0x1c6eb0[_0x95c97b(0x186)]&&this[_0x95c97b(0x147)](_0x2365a1)===_0x95c97b(0xa4)&&_0x2365a1['forEach']);}[_0xd5a59d(0xe4)](_0x13f89c,_0x154bef,_0x43a2af){return _0x43a2af['noFunctions']?typeof _0x13f89c[_0x154bef]=='function':!0x1;}[_0xd5a59d(0x17f)](_0x40d886){var _0x2b5971=_0xd5a59d,_0x508ff0='';return _0x508ff0=typeof _0x40d886,_0x508ff0===_0x2b5971(0x110)?this[_0x2b5971(0x147)](_0x40d886)==='[object\\\\x20Array]'?_0x508ff0=_0x2b5971(0x103):this[_0x2b5971(0x147)](_0x40d886)===_0x2b5971(0x98)?_0x508ff0=_0x2b5971(0x11d):this['_objectToString'](_0x40d886)===_0x2b5971(0xe5)?_0x508ff0='bigint':_0x40d886===null?_0x508ff0=_0x2b5971(0xfe):_0x40d886[_0x2b5971(0xf9)]&&(_0x508ff0=_0x40d886[_0x2b5971(0xf9)]['name']||_0x508ff0):_0x508ff0===_0x2b5971(0x8f)&&this['_HTMLAllCollection']&&_0x40d886 instanceof this[_0x2b5971(0x99)]&&(_0x508ff0=_0x2b5971(0x175)),_0x508ff0;}['_objectToString'](_0x589848){var _0x2742fa=_0xd5a59d;return Object['prototype']['toString'][_0x2742fa(0xe1)](_0x589848);}[_0xd5a59d(0xf4)](_0x52f421){var _0x28f82f=_0xd5a59d;return _0x52f421===_0x28f82f(0x105)||_0x52f421===_0x28f82f(0xb7)||_0x52f421===_0x28f82f(0x188);}[_0xd5a59d(0xe3)](_0x355bbd){var _0x51962f=_0xd5a59d;return _0x355bbd===_0x51962f(0xc7)||_0x355bbd===_0x51962f(0x146)||_0x355bbd===_0x51962f(0xee);}[_0xd5a59d(0xba)](_0x9ba98c,_0x596b39,_0x3e2452,_0x923cf0,_0x1f8e7a,_0x3ccbed){var _0x4e9c48=this;return function(_0x1e84c3){var _0x25e331=_0x22d4,_0x1119fe=_0x1f8e7a['node'][_0x25e331(0xb8)],_0x3061fc=_0x1f8e7a[_0x25e331(0x182)]['index'],_0x422100=_0x1f8e7a[_0x25e331(0x182)][_0x25e331(0xa2)];_0x1f8e7a[_0x25e331(0x182)][_0x25e331(0xa2)]=_0x1119fe,_0x1f8e7a[_0x25e331(0x182)]['index']=typeof _0x923cf0==_0x25e331(0x188)?_0x923cf0:_0x1e84c3,_0x9ba98c['push'](_0x4e9c48[_0x25e331(0x17e)](_0x596b39,_0x3e2452,_0x923cf0,_0x1f8e7a,_0x3ccbed)),_0x1f8e7a['node']['parent']=_0x422100,_0x1f8e7a[_0x25e331(0x182)][_0x25e331(0x102)]=_0x3061fc;};}['_addObjectProperty'](_0xf7c578,_0x51971d,_0x33c44e,_0x528a14,_0x297309,_0x4ead46,_0x51de28){var _0x240d55=_0xd5a59d,_0x3d8a22=this;return _0x51971d['_p_'+_0x297309[_0x240d55(0x12a)]()]=!0x0,function(_0x523495){var _0x2424bf=_0x240d55,_0x5d1930=_0x4ead46[_0x2424bf(0x182)][_0x2424bf(0xb8)],_0x36b6f9=_0x4ead46[_0x2424bf(0x182)][_0x2424bf(0x102)],_0x242217=_0x4ead46['node']['parent'];_0x4ead46['node'][_0x2424bf(0xa2)]=_0x5d1930,_0x4ead46[_0x2424bf(0x182)][_0x2424bf(0x102)]=_0x523495,_0xf7c578['push'](_0x3d8a22['_property'](_0x33c44e,_0x528a14,_0x297309,_0x4ead46,_0x51de28)),_0x4ead46[_0x2424bf(0x182)][_0x2424bf(0xa2)]=_0x242217,_0x4ead46[_0x2424bf(0x182)][_0x2424bf(0x102)]=_0x36b6f9;};}[_0xd5a59d(0x17e)](_0x2fcb0b,_0x50df32,_0x538478,_0x548d99,_0xb9e029){var _0x3006dd=_0xd5a59d,_0x20f0d9=this;_0xb9e029||(_0xb9e029=function(_0x1130b7,_0x5ae751){return _0x1130b7[_0x5ae751];});var _0x530633=_0x538478['toString'](),_0x10423d=_0x548d99[_0x3006dd(0xb6)]||{},_0x3d799d=_0x548d99[_0x3006dd(0xaf)],_0x3bdd25=_0x548d99['isExpressionToEvaluate'];try{var _0x55f627=this[_0x3006dd(0xed)](_0x2fcb0b),_0x274f99=_0x530633;_0x55f627&&_0x274f99[0x0]==='\\\\x27'&&(_0x274f99=_0x274f99[_0x3006dd(0x14c)](0x1,_0x274f99[_0x3006dd(0xf5)]-0x2));var _0x6b28ec=_0x548d99[_0x3006dd(0xb6)]=_0x10423d[_0x3006dd(0x10a)+_0x274f99];_0x6b28ec&&(_0x548d99[_0x3006dd(0xaf)]=_0x548d99['depth']+0x1),_0x548d99[_0x3006dd(0x134)]=!!_0x6b28ec;var _0x434c3=typeof _0x538478==_0x3006dd(0x126),_0xd44407={'name':_0x434c3||_0x55f627?_0x530633:this['_propertyName'](_0x530633)};if(_0x434c3&&(_0xd44407[_0x3006dd(0x126)]=!0x0),!(_0x50df32===_0x3006dd(0x103)||_0x50df32===_0x3006dd(0x136))){var _0xb21498=this[_0x3006dd(0x159)](_0x2fcb0b,_0x538478);if(_0xb21498&&(_0xb21498[_0x3006dd(0x155)]&&(_0xd44407[_0x3006dd(0x131)]=!0x0),_0xb21498[_0x3006dd(0x13d)]&&!_0x6b28ec&&!_0x548d99[_0x3006dd(0xdb)]))return _0xd44407[_0x3006dd(0x183)]=!0x0,this[_0x3006dd(0x93)](_0xd44407,_0x548d99),_0xd44407;}var _0x2d48a0;try{_0x2d48a0=_0xb9e029(_0x2fcb0b,_0x538478);}catch(_0x26552e){return _0xd44407={'name':_0x530633,'type':'unknown','error':_0x26552e[_0x3006dd(0x12d)]},this[_0x3006dd(0x93)](_0xd44407,_0x548d99),_0xd44407;}var _0x4844c2=this[_0x3006dd(0x17f)](_0x2d48a0),_0x2fcd14=this[_0x3006dd(0xf4)](_0x4844c2);if(_0xd44407[_0x3006dd(0x162)]=_0x4844c2,_0x2fcd14)this[_0x3006dd(0x93)](_0xd44407,_0x548d99,_0x2d48a0,function(){var _0x439489=_0x3006dd;_0xd44407['value']=_0x2d48a0[_0x439489(0x15c)](),!_0x6b28ec&&_0x20f0d9[_0x439489(0x15a)](_0x4844c2,_0xd44407,_0x548d99,{});});else{var _0x31d1c7=_0x548d99[_0x3006dd(0x156)]&&_0x548d99['level']<_0x548d99['autoExpandMaxDepth']&&_0x548d99[_0x3006dd(0xa5)][_0x3006dd(0x176)](_0x2d48a0)<0x0&&_0x4844c2!=='function'&&_0x548d99[_0x3006dd(0xa9)]<_0x548d99[_0x3006dd(0x14e)];_0x31d1c7||_0x548d99['level']<_0x3d799d||_0x6b28ec?(this[_0x3006dd(0xe8)](_0xd44407,_0x2d48a0,_0x548d99,_0x6b28ec||{}),this[_0x3006dd(0xbb)](_0x2d48a0,_0xd44407)):this[_0x3006dd(0x93)](_0xd44407,_0x548d99,_0x2d48a0,function(){var _0x5e1f7c=_0x3006dd;_0x4844c2===_0x5e1f7c(0xfe)||_0x4844c2===_0x5e1f7c(0x8f)||(delete _0xd44407[_0x5e1f7c(0xa7)],_0xd44407['capped']=!0x0);});}return _0xd44407;}finally{_0x548d99[_0x3006dd(0xb6)]=_0x10423d,_0x548d99[_0x3006dd(0xaf)]=_0x3d799d,_0x548d99[_0x3006dd(0x134)]=_0x3bdd25;}}['_capIfString'](_0x1e4d2e,_0x294ac4,_0x367bfb,_0x20ba06){var _0x58e966=_0xd5a59d,_0x3d317c=_0x20ba06['strLength']||_0x367bfb[_0x58e966(0xeb)];if((_0x1e4d2e===_0x58e966(0xb7)||_0x1e4d2e===_0x58e966(0x146))&&_0x294ac4['value']){let _0x5e11f9=_0x294ac4[_0x58e966(0xa7)][_0x58e966(0xf5)];_0x367bfb[_0x58e966(0x115)]+=_0x5e11f9,_0x367bfb[_0x58e966(0x115)]>_0x367bfb[_0x58e966(0xcf)]?(_0x294ac4[_0x58e966(0x10d)]='',delete _0x294ac4[_0x58e966(0xa7)]):_0x5e11f9>_0x3d317c&&(_0x294ac4[_0x58e966(0x10d)]=_0x294ac4[_0x58e966(0xa7)]['substr'](0x0,_0x3d317c),delete _0x294ac4[_0x58e966(0xa7)]);}}[_0xd5a59d(0xed)](_0x483f52){var _0x1cd051=_0xd5a59d;return!!(_0x483f52&&_0x1c6eb0[_0x1cd051(0xd1)]&&this['_objectToString'](_0x483f52)===_0x1cd051(0x132)&&_0x483f52[_0x1cd051(0x92)]);}[_0xd5a59d(0xd6)](_0x4af33b){var _0x552947=_0xd5a59d;if(_0x4af33b[_0x552947(0xe6)](/^\\\\d+$/))return _0x4af33b;var _0x5edc47;try{_0x5edc47=JSON[_0x552947(0xd5)](''+_0x4af33b);}catch{_0x5edc47='\\\\x22'+this[_0x552947(0x147)](_0x4af33b)+'\\\\x22';}return _0x5edc47['match'](/^\\\"([a-zA-Z_][a-zA-Z_0-9]*)\\\"$/)?_0x5edc47=_0x5edc47[_0x552947(0x14c)](0x1,_0x5edc47[_0x552947(0xf5)]-0x2):_0x5edc47=_0x5edc47[_0x552947(0xa1)](/'/g,'\\\\x5c\\\\x27')['replace'](/\\\\\\\\\\\"/g,'\\\\x22')['replace'](/(^\\\"|\\\"$)/g,'\\\\x27'),_0x5edc47;}['_processTreeNodeResult'](_0x24308a,_0xf8dc4c,_0x216094,_0x2d0ea1){var _0x47078e=_0xd5a59d;this[_0x47078e(0xc8)](_0x24308a,_0xf8dc4c),_0x2d0ea1&&_0x2d0ea1(),this[_0x47078e(0xbb)](_0x216094,_0x24308a),this[_0x47078e(0xda)](_0x24308a,_0xf8dc4c);}['_treeNodePropertiesBeforeFullValue'](_0x967358,_0x6a7c01){var _0x5538e8=_0xd5a59d;this[_0x5538e8(0xf7)](_0x967358,_0x6a7c01),this[_0x5538e8(0xab)](_0x967358,_0x6a7c01),this[_0x5538e8(0xa6)](_0x967358,_0x6a7c01),this[_0x5538e8(0x16f)](_0x967358,_0x6a7c01);}[_0xd5a59d(0xf7)](_0x281a69,_0x1fdaf3){}[_0xd5a59d(0xab)](_0x55e132,_0x287d38){}[_0xd5a59d(0x14a)](_0x415c7b,_0x581b77){}['_isUndefined'](_0xd3c5ae){return _0xd3c5ae===this['_undefined'];}[_0xd5a59d(0xda)](_0x100ee1,_0x1e47ad){var _0x192f28=_0xd5a59d;this['_setNodeLabel'](_0x100ee1,_0x1e47ad),this['_setNodeExpandableState'](_0x100ee1),_0x1e47ad[_0x192f28(0x174)]&&this[_0x192f28(0x16b)](_0x100ee1),this[_0x192f28(0x9f)](_0x100ee1,_0x1e47ad),this[_0x192f28(0x124)](_0x100ee1,_0x1e47ad),this['_cleanNode'](_0x100ee1);}['_additionalMetadata'](_0x13eec3,_0x31c974){var _0x5775f4=_0xd5a59d;try{_0x13eec3&&typeof _0x13eec3[_0x5775f4(0xf5)]=='number'&&(_0x31c974[_0x5775f4(0xf5)]=_0x13eec3[_0x5775f4(0xf5)]);}catch{}if(_0x31c974[_0x5775f4(0x162)]==='number'||_0x31c974['type']===_0x5775f4(0xee)){if(isNaN(_0x31c974[_0x5775f4(0xa7)]))_0x31c974[_0x5775f4(0x125)]=!0x0,delete _0x31c974[_0x5775f4(0xa7)];else switch(_0x31c974[_0x5775f4(0xa7)]){case Number[_0x5775f4(0x96)]:_0x31c974['positiveInfinity']=!0x0,delete _0x31c974['value'];break;case Number[_0x5775f4(0x123)]:_0x31c974[_0x5775f4(0x173)]=!0x0,delete _0x31c974[_0x5775f4(0xa7)];break;case 0x0:this['_isNegativeZero'](_0x31c974[_0x5775f4(0xa7)])&&(_0x31c974[_0x5775f4(0x168)]=!0x0);break;}}else _0x31c974[_0x5775f4(0x162)]===_0x5775f4(0x144)&&typeof _0x13eec3[_0x5775f4(0x17b)]==_0x5775f4(0xb7)&&_0x13eec3[_0x5775f4(0x17b)]&&_0x31c974[_0x5775f4(0x17b)]&&_0x13eec3[_0x5775f4(0x17b)]!==_0x31c974[_0x5775f4(0x17b)]&&(_0x31c974[_0x5775f4(0xbc)]=_0x13eec3[_0x5775f4(0x17b)]);}[_0xd5a59d(0x118)](_0x3f62bd){return 0x1/_0x3f62bd===Number['NEGATIVE_INFINITY'];}[_0xd5a59d(0x16b)](_0x2fe6ed){var _0x387e2c=_0xd5a59d;!_0x2fe6ed[_0x387e2c(0xde)]||!_0x2fe6ed[_0x387e2c(0xde)][_0x387e2c(0xf5)]||_0x2fe6ed['type']===_0x387e2c(0x103)||_0x2fe6ed[_0x387e2c(0x162)]===_0x387e2c(0xd1)||_0x2fe6ed[_0x387e2c(0x162)]===_0x387e2c(0x186)||_0x2fe6ed['props']['sort'](function(_0x3b9b24,_0x56671c){var _0x509fa8=_0x387e2c,_0x4f6678=_0x3b9b24[_0x509fa8(0x17b)]['toLowerCase'](),_0x390f8d=_0x56671c[_0x509fa8(0x17b)]['toLowerCase']();return _0x4f6678<_0x390f8d?-0x1:_0x4f6678>_0x390f8d?0x1:0x0;});}['_addFunctionsNode'](_0xdef430,_0x1d4f47){var _0x1a0043=_0xd5a59d;if(!(_0x1d4f47[_0x1a0043(0xb9)]||!_0xdef430[_0x1a0043(0xde)]||!_0xdef430[_0x1a0043(0xde)]['length'])){for(var _0x15afac=[],_0x109a59=[],_0x281f7a=0x0,_0x5cdfbc=_0xdef430[_0x1a0043(0xde)]['length'];_0x281f7a<_0x5cdfbc;_0x281f7a++){var _0xa80335=_0xdef430['props'][_0x281f7a];_0xa80335[_0x1a0043(0x162)]===_0x1a0043(0x144)?_0x15afac['push'](_0xa80335):_0x109a59[_0x1a0043(0xb1)](_0xa80335);}if(!(!_0x109a59[_0x1a0043(0xf5)]||_0x15afac[_0x1a0043(0xf5)]<=0x1)){_0xdef430[_0x1a0043(0xde)]=_0x109a59;var _0x20b1db={'functionsNode':!0x0,'props':_0x15afac};this[_0x1a0043(0xf7)](_0x20b1db,_0x1d4f47),this['_setNodeLabel'](_0x20b1db,_0x1d4f47),this[_0x1a0043(0x112)](_0x20b1db),this['_setNodePermissions'](_0x20b1db,_0x1d4f47),_0x20b1db['id']+='\\\\x20f',_0xdef430[_0x1a0043(0xde)][_0x1a0043(0xef)](_0x20b1db);}}}['_addLoadNode'](_0x2d4ed7,_0x10c69f){}[_0xd5a59d(0x112)](_0x1eb55a){}[_0xd5a59d(0xd0)](_0x471991){var _0x235d83=_0xd5a59d;return Array[_0x235d83(0x97)](_0x471991)||typeof _0x471991==_0x235d83(0x110)&&this[_0x235d83(0x147)](_0x471991)===_0x235d83(0xca);}[_0xd5a59d(0x16f)](_0x573363,_0x378b53){}[_0xd5a59d(0x17c)](_0x1bd6da){var _0xbe21f4=_0xd5a59d;delete _0x1bd6da[_0xbe21f4(0x178)],delete _0x1bd6da['_hasSetOnItsPath'],delete _0x1bd6da[_0xbe21f4(0x149)];}[_0xd5a59d(0xa6)](_0x35d1ef,_0x4ed53b){}}let _0x53d974=new _0x4a2137(),_0x3eb1c5={'props':0x64,'elements':0x64,'strLength':0x400*0x32,'totalStrLength':0x400*0x32,'autoExpandLimit':0x1388,'autoExpandMaxDepth':0xa},_0x550602={'props':0x5,'elements':0x5,'strLength':0x100,'totalStrLength':0x100*0x3,'autoExpandLimit':0x1e,'autoExpandMaxDepth':0x2};function _0x259d6c(_0x2e3779,_0xd917b2,_0xb327ac,_0x24707f,_0x4ad55e,_0x4f5466){var _0x4b9f72=_0xd5a59d;let _0x4ea472,_0x5b4197;try{_0x5b4197=_0x57d945(),_0x4ea472=_0x51de18[_0xd917b2],!_0x4ea472||_0x5b4197-_0x4ea472['ts']>0x1f4&&_0x4ea472[_0x4b9f72(0x11f)]&&_0x4ea472[_0x4b9f72(0x137)]/_0x4ea472[_0x4b9f72(0x11f)]<0x64?(_0x51de18[_0xd917b2]=_0x4ea472={'count':0x0,'time':0x0,'ts':_0x5b4197},_0x51de18[_0x4b9f72(0x17a)]={}):_0x5b4197-_0x51de18[_0x4b9f72(0x17a)]['ts']>0x32&&_0x51de18[_0x4b9f72(0x17a)][_0x4b9f72(0x11f)]&&_0x51de18[_0x4b9f72(0x17a)][_0x4b9f72(0x137)]/_0x51de18[_0x4b9f72(0x17a)]['count']<0x64&&(_0x51de18[_0x4b9f72(0x17a)]={});let _0x10d134=[],_0x59cb67=_0x4ea472[_0x4b9f72(0x142)]||_0x51de18[_0x4b9f72(0x17a)][_0x4b9f72(0x142)]?_0x550602:_0x3eb1c5,_0x594003=_0x5e3009=>{var _0x2d7700=_0x4b9f72;let _0xa0775f={};return _0xa0775f[_0x2d7700(0xde)]=_0x5e3009[_0x2d7700(0xde)],_0xa0775f[_0x2d7700(0xcd)]=_0x5e3009['elements'],_0xa0775f['strLength']=_0x5e3009['strLength'],_0xa0775f[_0x2d7700(0xcf)]=_0x5e3009['totalStrLength'],_0xa0775f[_0x2d7700(0x14e)]=_0x5e3009[_0x2d7700(0x14e)],_0xa0775f[_0x2d7700(0xe9)]=_0x5e3009[_0x2d7700(0xe9)],_0xa0775f[_0x2d7700(0x174)]=!0x1,_0xa0775f['noFunctions']=!_0x552919,_0xa0775f[_0x2d7700(0xaf)]=0x1,_0xa0775f[_0x2d7700(0xe0)]=0x0,_0xa0775f[_0x2d7700(0x91)]=_0x2d7700(0x18a),_0xa0775f[_0x2d7700(0xfb)]=_0x2d7700(0x13a),_0xa0775f[_0x2d7700(0x156)]=!0x0,_0xa0775f['autoExpandPreviousObjects']=[],_0xa0775f[_0x2d7700(0xa9)]=0x0,_0xa0775f[_0x2d7700(0xdb)]=!0x0,_0xa0775f[_0x2d7700(0x115)]=0x0,_0xa0775f[_0x2d7700(0x182)]={'current':void 0x0,'parent':void 0x0,'index':0x0},_0xa0775f;};for(var _0x4e3ef1=0x0;_0x4e3ef1<_0x4ad55e[_0x4b9f72(0xf5)];_0x4e3ef1++)_0x10d134[_0x4b9f72(0xb1)](_0x53d974[_0x4b9f72(0xe8)]({'timeNode':_0x2e3779===_0x4b9f72(0x137)||void 0x0},_0x4ad55e[_0x4e3ef1],_0x594003(_0x59cb67),{}));if(_0x2e3779===_0x4b9f72(0xad)||_0x2e3779===_0x4b9f72(0x16a)){let _0x597c0d=Error[_0x4b9f72(0x153)];try{Error['stackTraceLimit']=0x1/0x0,_0x10d134['push'](_0x53d974[_0x4b9f72(0xe8)]({'stackNode':!0x0},new Error()[_0x4b9f72(0x163)],_0x594003(_0x59cb67),{'strLength':0x1/0x0}));}finally{Error[_0x4b9f72(0x153)]=_0x597c0d;}}return{'method':_0x4b9f72(0xd7),'version':_0x533c9c,'args':[{'ts':_0xb327ac,'session':_0x24707f,'args':_0x10d134,'id':_0xd917b2,'context':_0x4f5466}]};}catch(_0x541082){return{'method':'log','version':_0x533c9c,'args':[{'ts':_0xb327ac,'session':_0x24707f,'args':[{'type':'unknown','error':_0x541082&&_0x541082['message']}],'id':_0xd917b2,'context':_0x4f5466}]};}finally{try{if(_0x4ea472&&_0x5b4197){let _0x24841e=_0x57d945();_0x4ea472['count']++,_0x4ea472[_0x4b9f72(0x137)]+=_0x1a1b80(_0x5b4197,_0x24841e),_0x4ea472['ts']=_0x24841e,_0x51de18[_0x4b9f72(0x17a)][_0x4b9f72(0x11f)]++,_0x51de18['hits'][_0x4b9f72(0x137)]+=_0x1a1b80(_0x5b4197,_0x24841e),_0x51de18[_0x4b9f72(0x17a)]['ts']=_0x24841e,(_0x4ea472[_0x4b9f72(0x11f)]>0x32||_0x4ea472['time']>0x64)&&(_0x4ea472[_0x4b9f72(0x142)]=!0x0),(_0x51de18['hits']['count']>0x3e8||_0x51de18['hits'][_0x4b9f72(0x137)]>0x12c)&&(_0x51de18['hits']['reduceLimits']=!0x0);}}catch{}}}return _0x259d6c;}((_0x5e47ad,_0x378d13,_0x36c5af,_0x2ad400,_0x3b6c41,_0x3d43c6,_0x2ffba0,_0xb9f3b,_0x1807ca,_0x39281c,_0xab83d7)=>{var _0x3e722f=_0x2521ed;if(_0x5e47ad[_0x3e722f(0xf8)])return _0x5e47ad[_0x3e722f(0xf8)];if(!X(_0x5e47ad,_0xb9f3b,_0x3b6c41))return _0x5e47ad['_console_ninja']={'consoleLog':()=>{},'consoleTrace':()=>{},'consoleTime':()=>{},'consoleTimeEnd':()=>{},'autoLog':()=>{},'autoLogMany':()=>{},'autoTraceMany':()=>{},'coverage':()=>{},'autoTrace':()=>{},'autoTime':()=>{},'autoTimeEnd':()=>{}},_0x5e47ad[_0x3e722f(0xf8)];let _0x48b6db=B(_0x5e47ad),_0x57ac12=_0x48b6db['elapsed'],_0xe33db9=_0x48b6db[_0x3e722f(0x120)],_0x526af0=_0x48b6db[_0x3e722f(0x15d)],_0x1dd57c={'hits':{},'ts':{}},_0x3d768e=J(_0x5e47ad,_0x1807ca,_0x1dd57c,_0x3d43c6),_0x5f2366=_0x361547=>{_0x1dd57c['ts'][_0x361547]=_0xe33db9();},_0x2958f1=(_0x36dda4,_0x58c809)=>{var _0x8e36ec=_0x3e722f;let _0x3e9662=_0x1dd57c['ts'][_0x58c809];if(delete _0x1dd57c['ts'][_0x58c809],_0x3e9662){let _0xc25b2=_0x57ac12(_0x3e9662,_0xe33db9());_0x3833c3(_0x3d768e(_0x8e36ec(0x137),_0x36dda4,_0x526af0(),_0x4e4a80,[_0xc25b2],_0x58c809));}},_0x55e353=_0x3c2726=>{var _0x4c18bf=_0x3e722f,_0x470830;return _0x3b6c41==='next.js'&&_0x5e47ad[_0x4c18bf(0x11b)]&&((_0x470830=_0x3c2726==null?void 0x0:_0x3c2726[_0x4c18bf(0x157)])==null?void 0x0:_0x470830[_0x4c18bf(0xf5)])&&(_0x3c2726[_0x4c18bf(0x157)][0x0]['origin']=_0x5e47ad['origin']),_0x3c2726;};_0x5e47ad['_console_ninja']={'consoleLog':(_0xb1db27,_0x4d7fde)=>{var _0x41fac5=_0x3e722f;_0x5e47ad[_0x41fac5(0xcc)]['log']['name']!==_0x41fac5(0x119)&&_0x3833c3(_0x3d768e(_0x41fac5(0xd7),_0xb1db27,_0x526af0(),_0x4e4a80,_0x4d7fde));},'consoleTrace':(_0x3d2b3f,_0x29f758)=>{var _0x3bb354=_0x3e722f,_0x24fd68,_0x565109;_0x5e47ad[_0x3bb354(0xcc)][_0x3bb354(0xd7)]['name']!==_0x3bb354(0x18e)&&((_0x565109=(_0x24fd68=_0x5e47ad['process'])==null?void 0x0:_0x24fd68['versions'])!=null&&_0x565109[_0x3bb354(0x182)]&&(_0x5e47ad[_0x3bb354(0x169)]=!0x0),_0x3833c3(_0x55e353(_0x3d768e('trace',_0x3d2b3f,_0x526af0(),_0x4e4a80,_0x29f758))));},'consoleError':(_0x2905a8,_0x5e8589)=>{var _0x404511=_0x3e722f;_0x5e47ad[_0x404511(0x169)]=!0x0,_0x3833c3(_0x55e353(_0x3d768e(_0x404511(0x16a),_0x2905a8,_0x526af0(),_0x4e4a80,_0x5e8589)));},'consoleTime':_0x2fbfbe=>{_0x5f2366(_0x2fbfbe);},'consoleTimeEnd':(_0x259732,_0x559171)=>{_0x2958f1(_0x559171,_0x259732);},'autoLog':(_0x40568e,_0x2ce346)=>{var _0x5b92d9=_0x3e722f;_0x3833c3(_0x3d768e(_0x5b92d9(0xd7),_0x2ce346,_0x526af0(),_0x4e4a80,[_0x40568e]));},'autoLogMany':(_0x382967,_0x4639f2)=>{var _0x5698ae=_0x3e722f;_0x3833c3(_0x3d768e(_0x5698ae(0xd7),_0x382967,_0x526af0(),_0x4e4a80,_0x4639f2));},'autoTrace':(_0x85edef,_0x1971fc)=>{var _0x323975=_0x3e722f;_0x3833c3(_0x55e353(_0x3d768e(_0x323975(0xad),_0x1971fc,_0x526af0(),_0x4e4a80,[_0x85edef])));},'autoTraceMany':(_0x423cb2,_0x132601)=>{var _0x429ba5=_0x3e722f;_0x3833c3(_0x55e353(_0x3d768e(_0x429ba5(0xad),_0x423cb2,_0x526af0(),_0x4e4a80,_0x132601)));},'autoTime':(_0x4093c2,_0x187089,_0x3a3847)=>{_0x5f2366(_0x3a3847);},'autoTimeEnd':(_0xd69daa,_0x4e00cb,_0x12447b)=>{_0x2958f1(_0x4e00cb,_0x12447b);},'coverage':_0x28f0a5=>{var _0x5963e3=_0x3e722f;_0x3833c3({'method':_0x5963e3(0x13e),'version':_0x3d43c6,'args':[{'id':_0x28f0a5}]});}};let _0x3833c3=H(_0x5e47ad,_0x378d13,_0x36c5af,_0x2ad400,_0x3b6c41,_0x39281c,_0xab83d7),_0x4e4a80=_0x5e47ad['_console_ninja_session'];return _0x5e47ad[_0x3e722f(0xf8)];})(globalThis,_0x2521ed(0x10f),_0x2521ed(0xb0),_0x2521ed(0xfc),_0x2521ed(0x12c),'1.0.0','1753940532969',_0x2521ed(0xc3),_0x2521ed(0x10e),'','1');\");\n    } catch (e) {}\n}\nfunction oo_oo(i) {\n    for(var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        v[_key - 1] = arguments[_key];\n    }\n    try {\n        oo_cm().consoleLog(i, v);\n    } catch (e) {}\n    return v;\n}\noo_oo; /* istanbul ignore next */ \nfunction oo_tr(i) {\n    for(var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        v[_key - 1] = arguments[_key];\n    }\n    try {\n        oo_cm().consoleTrace(i, v);\n    } catch (e) {}\n    return v;\n}\noo_tr; /* istanbul ignore next */ \nfunction oo_tx(i) {\n    for(var _len = arguments.length, v = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        v[_key - 1] = arguments[_key];\n    }\n    try {\n        oo_cm().consoleError(i, v);\n    } catch (e) {}\n    return v;\n}\noo_tx; /* istanbul ignore next */ \nfunction oo_ts(v) {\n    try {\n        oo_cm().consoleTime(v);\n    } catch (e) {}\n    return v;\n}\noo_ts; /* istanbul ignore next */ \nfunction oo_te(v, i) {\n    try {\n        oo_cm().consoleTimeEnd(v, i);\n    } catch (e) {}\n    return v;\n}\noo_te; /*eslint unicorn/no-abusive-eslint-disable:,eslint-comments/disable-enable-pair:,eslint-comments/no-unlimited-disable:,eslint-comments/no-aggregating-enable:,eslint-comments/no-duplicate-disable:,eslint-comments/no-unused-disable:,eslint-comments/no-unused-enable:,*/ \nvar _c;\n$RefreshReg$(_c, \"CreateTrackSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/createTracksheet/createTrackSheet.tsx\n"));

/***/ })

});